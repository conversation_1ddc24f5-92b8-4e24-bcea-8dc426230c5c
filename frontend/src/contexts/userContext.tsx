// src/context/UserContext.tsx

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {Subject, Task } from '../types/student';

interface TestHistory {
    _id: string;
    class: string;
    subject: string;
    testType: string;
    topics: string[];
    questions: string[];
    testDate: Date;
    startTime: string;
    duration: number;
    numberOfQuestions: number;
    totalMarks: number;
    testInstructions: string;
    createdBy: string;
    createdAt?: Date;
    updatedAt?: Date;
}

interface Class {
    _id: string;
    classCode: string;
    schoolId: string; // ObjectId as string
    teacherId: string[]; // Array of ObjectIds as strings
    subject: string;
    className: string;
    students: string[]; // Array of ObjectIds as strings
  }

interface User {
    usertype?: string;
    id?: string;
    proficiency?: string;
    profileImage?: string;
    admissionNumber?: string;
    email?: string;
    username?: string;
    firstName?: string;
    lastName?: string;
    classId?: string;
    schoolCode?: string;
    subjects?: Array<Subject>;
    role?: string;
    tasks?: Array<Task>;
    accessToken?: string;
    classes?: Array<Class>;
    testHistory?: Array<TestHistory>;
    // Add any other fields as needed
}

interface UserContextType {
    user: User | null;
    setUser: (user: User | null) => void;
}

// Create the context with a default value
const UserContext = createContext<UserContextType | undefined>(undefined);

// Create the provider component
export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);

    const handleSetUser = (newUser: User | null) => { // callback function to set the user correctly
      console.log('UserContext: handleSetUser called', { newUser, prevUser: user });
      setUser((prevUser) => {
        if (newUser === null) {
          console.log('UserContext: Clearing user data');
          return null; // clear the user data
        }
      return { ...prevUser, ...newUser };
      })
    }

    // Don't automatically load from sessionStorage - let usePageRefresh handle initialization
    // This prevents race conditions between user context and usePageRefresh
    // useEffect(() => {
    //     const storedUserEmail = sessionStorage.getItem('email');
    //     const storedRole = sessionStorage.getItem('role');
    //     console.log('UserContext: Loading from sessionStorage', { storedUserEmail, storedRole });
    //     if (storedUserEmail) {
    //         console.log('UserContext: Setting user from sessionStorage');
    //         setUser({email: storedUserEmail, role: storedRole ?? ''});
    //     }
    // }, []);

    // Save user data to sessionStorage whenever it changes
    useEffect(() => {
        if (user) {
            console.log(`[INFO] user changed: ${JSON.stringify(user?.accessToken)}`);
            sessionStorage.setItem('email', user?.email ?? user?.username ?? '');
            sessionStorage.setItem('role', user?.role ?? '');
        } else {
            sessionStorage.removeItem('email'); // Clear when user logs out
            sessionStorage.removeItem('role');
            sessionStorage.removeItem('sessionId'); // Clear session tracking
        }
    }, [user]);

    return (
        <UserContext.Provider value={{ user, setUser: handleSetUser }}>
            {children}
        </UserContext.Provider>
    );
};

// Custom hook to use the UserContext
export const useUser = (): UserContextType => {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
};
