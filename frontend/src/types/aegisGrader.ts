// --- Enums ---
export enum GradingStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN PROGRESS',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED'
}

export interface ScannedPage {
    imageUrl: string; // dataURL
    timestamp: number;
}

export interface TestDetails {
    className: string;
    subject: string;
    date: string; // YYYY-MM-DD
}

export interface TestDocument {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl: string; // Original filename
    timestamp: number;
    pdfData?: string; // Base64 dataURL - Optional initially
    className: string;
    uploading?: boolean;
    error?: string; // Add error field per document
    evaluationResult?: {
        evaluation: {
            total_marks: string;
            maximum_possible_marks: string;
            percentage_score: string;
            section: Array<{
                name: string;
                section_marks: string;
                section_possible_marks: string;
                question: Array<{
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                }> | {
                    number: string;
                    marks_awarded: string;
                    marks_possible: string;
                    feedback: string;
                    marks_breakdown: {
                        criterion: Array<{
                            _: string;
                            name: string;
                        }>;
                    };
                };
            }>;
        };
    };
}

export interface RubricDocument {
    type: 'rubric' | 'questionPaper';
    pdfUrl: string; // Original filename
    pdfData: string; // Base64 dataURL
    timestamp: number;
}

export interface TestSubmission {
    id?: string;
    testDetails: TestDetails;
    answerSheets: TestDocument[]; // Should contain pdfData for submission
    questionPaper?: RubricDocument;
    rubric?: RubricDocument;
    status?: GradingStatus;
    gradingProgress?: number; // Optional: 0-100
    results?: any; // To store grading results from backend
}