import React, { useEffect, useState } from 'react';
import {
    EnvelopeIcon, PhoneIcon, AcademicCapIcon,
    PencilIcon, CheckIcon, XMarkIcon, ArrowLeftStartOnRectangleIcon,
    CameraIcon,
    MapPinIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';

import { createPortal } from 'react-dom';
import { fetchWithCache } from '@/utils/cacheUtil';
import FeedBackComp from '@/components/Feedback';
import ThemeToggle from '@/components/ThemeToggle';

interface UserProfile {
    username: string;
    email: string;
    phoneNumber: string;
    occupation: string;
    profileImage: string;
}

const motivationalQuotes = [
    "Keep learning, keep growing!",
    "Every day is a chance to learn something new.",
    "Teaching is the one profession that creates all others.",
    "Education is the passport to the future.",
    "Empower, inspire, achieve.",
];

const ProfilePage: React.FC = () => {
    const { user, setUser } = useUser();
    const [isEditing, setIsEditing] = useState(false);
    const [userProfile, setUserProfile] = useState<UserProfile>({
        username: user?.username ?? 'Alex',
        email: user?.email ?? '<EMAIL>',
        phoneNumber: '+****************',
        occupation: user?.role ?? 'Unknown',
        profileImage: user?.profileImage ?? '/api/placeholder/200/200' // <-- Prefer user.profileImage
    });

    const [schoolInfo, setSchoolInfo] = useState<any>({});

    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const [about, setAbout] = useState<string>("Passionate learner and technology enthusiast.");
    const [editedProfile, setEditedProfile] = useState<UserProfile>({ ...userProfile });
    const [avatarPreview, setAvatarPreview] = useState<string>(userProfile.profileImage);

    const axiosPrivate = useAxiosPrivate();
    const navigate = useNavigate();



    useEffect(() => {
        console.error('User:', user);
        if (!user?.schoolCode) {
            return;
        }
        const fetchSchoolInfo = async () => {
            try {
                const response = await fetchWithCache(axiosPrivate, `/api/details/getSchoolDetailsByCode/${user?.schoolCode}`);
                console.error('School Info:', response);
                if (response) {
                    setSchoolInfo(response);
                }
            } catch (error) {
                console.error('Error fetching school info:', error);
            }
        };
        if (user?.role) {
            fetchSchoolInfo();
        }
    }, [user?.role, axiosPrivate]);

    const handleLogout = async () => {
        const logoutToast = toast.loading('Logging out...', { position: "top-right", toastId: 'logout' });
        try {
            let payload: any = {};
            if (user?.role === 'Student') {
                payload = { admissionNumber: user?.admissionNumber, email: user?.email};
            } else {
                payload = { email: user?.email };
            }
            await axiosPrivate.put(`/api/logout/${user?.role}`, payload);
            toast.update(logoutToast, {
                render: 'Logged out successfully!',
                type: 'success',
                isLoading: false,
                autoClose: 1500
            });
        } catch (error) {
            toast.update(logoutToast, {
                render: 'Failed to logout!',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
        } finally {
            setUser(null);
            sessionStorage.clear();
            setTimeout(() => navigate('/'), 1500);
        }
    };
    const syncServ = async (orig_username: string, new_email: string, new_uname: string) => {
        try {
            await axiosPrivate.post(`/api/details/editDetail${user?.role}`, { o_uname: orig_username, new_email: new_email, new_uname: new_uname });
        } catch (e) {
            console.error(`error: ${e}`);
        }
    };

    const handleSave = () => {
        syncServ(userProfile.username, editedProfile.email, editedProfile.username);
        setUserProfile({ ...editedProfile });
        setIsEditing(false);
    };

    const handleCancel = () => {
        setEditedProfile({ ...userProfile });
        setIsEditing(false);
    };

    const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            // 1. Get presigned URL from backend
            try {
                const fileType = file.type;
                const response = await axiosPrivate.post('/api/image/getProfilePhotoPresignedUrl', {
                    userId: user?.id,
                    fileType
                });
                const { url, key } = response.data;
                await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': fileType,
                        'x-amz-acl': 'public-read',
                        'x-amz-content-sha256': 'UNSIGNED-PAYLOAD'
                    },
                    body: file
                });
                const s3Url = `https://${process.env.REACT_APP_S3_BUCKET}.s3.${process.env.REACT_APP_AWS_REGION}.amazonaws.com/${key}`;
                setAvatarPreview(s3Url);
                setEditedProfile({ ...editedProfile, profileImage: s3Url });
            } catch (err) {
                toast.error('Failed to upload profile photo');
            }
        }
    };

    // Pick a random motivational quote
    const quote = motivationalQuotes[
        (user?.username?.charCodeAt(0) || 1) % motivationalQuotes.length
    ];


    return (
        <div className="min-h-screen bg-background flex flex-col items-center pb-16">
            {/* Hero Header */}
            <div className="w-full bg-card py-6 px-3 sm:px-6 md:px-10 flex flex-col sm:flex-row items-center sm:items-end justify-between gap-4 sm:gap-6 shadow-sm">
                <div className="flex items-center gap-4 sm:gap-6 w-full sm:w-auto">
                    <div className="relative">
                        {/* Avatar */}
                        <div className="w-24 h-24 sm:w-32 sm:h-32 md:w-36 md:h-36 rounded-full bg-gradient-to-tr from-accent to-primary flex items-center justify-center text-4xl sm:text-5xl md:text-6xl font-extrabold text-white shadow-xl overflow-hidden" style={{ minWidth: '6rem' }}>
                            {avatarPreview && avatarPreview !== '/api/placeholder/200/200' ? (
                                <img
                                    src={avatarPreview}
                                    alt="Avatar"
                                    className="w-full h-full object-cover rounded-full"
                                />
                            ) : (
                                userProfile.username[0]
                            )}
                        </div>
                        {isEditing && (
                            <label className="absolute bottom-2 left-10 sm:left-16 md:left-20 bg-white/90 rounded-full p-2 shadow-lg cursor-pointer border border-border flex items-center justify-center group transition-all duration-200"
                                title="Change avatar"
                                tabIndex={0}
                                aria-label="Change avatar"
                            >
                                {/* Camera Icon */}
                                <CameraIcon className="w-5 h-5 text-accent group-hover:text-primary transition-all duration-200" />
                                <span className="absolute left-full ml-2 opacity-0 group-hover:opacity-100 bg-black text-white text-xs rounded px-2 py-1 pointer-events-none transition-opacity duration-200 z-10">
                                    Change avatar
                                </span>
                                <input
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleAvatarChange}
                                />
                            </label>
                        )}
                    </div>
                    <div className="flex flex-col gap-1 sm:gap-2">
                        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-primary leading-tight">{userProfile.username}</h1>
                        <span className="inline-block px-3 py-1 rounded-full bg-accent/10 text-accent text-xs sm:text-sm font-semibold uppercase shadow-sm w-fit">
                            {userProfile.occupation}
                        </span>
                        <span className="text-muted-foreground text-xs sm:text-base italic mt-1">{quote}</span>
                    </div>
                </div>
                <div className="flex gap-2 sm:gap-3 w-full sm:w-auto justify-end mt-4 sm:mt-0">
                    {isEditing ? (
                        <>
                            <button
                                onClick={handleCancel}
                                className="bg-white hover:bg-white/70 text-gray-700 px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <XMarkIcon className="w-5 h-5 inline mr-1" /> Cancel
                            </button>
                            <button
                                onClick={handleSave}
                                className="bg-accent hover:bg-accent/70 text-white px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <CheckIcon className="w-5 h-5 inline mr-1" /> Save
                            </button>
                        </>
                    ) : (
                        <>
                            <div className='block lg:hidden md:hidden px-4 py-2'>
                                <ThemeToggle size="default" />
                            </div>
                            <button
                                onClick={() => setIsEditing(true)}
                                className="bg-accent/10 hover:bg-accent/20 text-accent px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <PencilIcon className="w-5 h-5 inline mr-1" /> Edit
                            </button>
                            <button
                                onClick={() => setShowLogoutModal(true)}
                                className="bg-blend-saturation hover:bg-card text-red-500 px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                                title="Logout"
                            >
                                <ArrowLeftStartOnRectangleIcon className="w-5 h-5 inline mr-1" /> Logout
                            </button>
                        </>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 w-full flex flex-col items-center justify-start mt-4">
                <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4 md:px-0 lg:px-0 px-2">
                    <div className="bg-card rounded-xl border border-border p-4 sm:p-6 md:p-8 flex flex-col gap-6 shadow-none">
                        {/* Account Info */}
                        <div>
                            <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Account Info</h2>
                            <div className="flex items-center gap-2 sm:gap-3 mb-2">
                                <EnvelopeIcon className="w-5 h-5 text-accent" />
                                {isEditing ? (
                                    <input
                                        type="email"
                                        value={editedProfile.email}
                                        onChange={e => setEditedProfile({ ...editedProfile, email: e.target.value })}
                                        className="flex-1 border-b border-border py-2 focus:border-accent outline-none text-primary bg-transparent text-sm sm:text-base transition-all duration-200"
                                    />
                                ) : (
                                    <span className="flex-1 text-primary text-sm sm:text-base">{userProfile.email}</span>
                                )}
                            </div>
                            {/* <div className="flex items-center gap-2 sm:gap-3">
                                <PhoneIcon className="w-5 h-5 text-accent" />
                                {isEditing ? (
                                    <input
                                        type="tel"
                                        value={editedProfile.phoneNumber}
                                        onChange={e => setEditedProfile({ ...editedProfile, phoneNumber: e.target.value })}
                                        className="flex-1 border-b border-border py-2 focus:border-accent outline-none text-primary bg-transparent text-sm sm:text-base transition-all duration-200"
                                    />
                                ) : (
                                    <span className="flex-1 text-primary text-sm sm:text-base">{userProfile.phoneNumber}</span>
                                )}
                            </div> */}
                        </div>
                        {/* Institute Info */}
                        <div>
                            <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Institute Information</h2>
                            <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                <AcademicCapIcon className="w-5 h-5 text-accent" />
                                <span className="text-primary text-sm sm:text-base font-medium">{schoolInfo.name}</span>
                            </div>
                            <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                <MapPinIcon className="w-5 h-5 text-accent" />
                                <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.pincode}</span>
                            </div>
                            {/* <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                <EnvelopeIcon className="w-5 h-5 text-accent" />
                                <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.phone}</span>
                            </div> */}
                            <div className="flex items-center gap-2 sm:gap-3">
                                <PhoneIcon className="w-5 h-5 text-accent" />
                                <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.phone}</span>
                            </div>
                        </div>
                    </div>
                    {/* <div className="bg-card rounded-xl border border-border p-4 sm:p-6 md:p-8 flex flex-col gap-6 shadow-none h-fit">
                        <div>
                            <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Security Settings</h2>
                            <div className="flex flex-col gap-2 sm:gap-3">
                                <div className="flex items-center justify-between py-2 border-b border-border last:border-b-0">
                                    <div className="flex items-center gap-2 sm:gap-3">
                                        <LockClosedIcon className="w-5 h-5 text-accent" />
                                        <span className="text-sm sm:text-base">Change Password</span>
                                    </div>
                                    <button className="text-accent text-xs font-medium hover:underline rounded-lg px-2 py-1 transition-all duration-200">
                                        Change
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div> */}
                </div>
            </div>
            {/* Logout Confirmation Modal */}
            {showLogoutModal && createPortal(
                <div className="fixed inset-0 backdrop-blur-sm z-50 flex items-center justify-center">
                    <div className="bg-card border border-border rounded-2xl shadow-xl p-4 w-full max-w-sm transition-shadow duration-200">
                        <h2 className="text-xl font-bold text-primary mb-2">Confirm Logout</h2>
                        <p className="text-muted-foreground mb-2">Are you sure you want to logout?</p>
                        <div className="flex justify-end gap-2">
                            <button
                                onClick={() => setShowLogoutModal(false)}
                                className="px-5 py-2 rounded-xl bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-200"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={() => {
                                    setShowLogoutModal(false);
                                    handleLogout();
                                }}
                                className="px-5 py-2 rounded-xl bg-red-500 text-white hover:bg-red-600 transition-all duration-200"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>,
                document.body
            )}
          <FeedBackComp id={user?.id} role={user?.role}/>
        </div>
    );
};

export default ProfilePage;
