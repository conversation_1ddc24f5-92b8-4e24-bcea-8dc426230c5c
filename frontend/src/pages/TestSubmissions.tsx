import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, UsersIcon, TrophyIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';

interface StudentSubmission {
  studentId: string;
  studentName: string;
  email?: string;
  profileImage?: string;
  score: number;
  percentage: number;
  timeSpent: number;
  submittedAt: Date | null;
  status: 'completed' | 'in-progress' | 'not-attempted';
}

const TestSubmissions = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const axiosPrivate = useAxiosPrivate();

  const { testId, testDetails } = location.state || {};
  const [submissions, setSubmissions] = useState<StudentSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'score' | 'time'>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  // console.log("test details: ", testDetails);

  useEffect(() => {
    if (!testId || !testDetails) {
      navigate('/teacher-dashboard');
      return;
    }

    fetchSubmissions();
  }, [testId, testDetails]);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);

      console.log('Fetching submissions for testId:', testId);
      // Fetch actual test submissions from the API using authenticated axios
      const response = await axiosPrivate.get(`/api/test/${testId}/submissions`);
      const data = response.data;

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch submissions');
      }

      // Transform submissions to ensure dates are properly parsed
      const transformedSubmissions = data.submissions.map((submission: any) => ({
        ...submission,
        submittedAt: submission.submittedAt ? new Date(submission.submittedAt) : null
      }));

      setSubmissions(transformedSubmissions);

    } catch (error) {
      console.error('Error fetching submissions:', error);
      // Fallback to empty state on error
      setSubmissions([]);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (minutes: number): string => {
    return `${minutes} MIN`;
  };

  const formatDateTimeIST = (date: Date, startTime?: string): string => {
    const testDate = new Date(date);
    if (startTime) {
      const [hours, minutes] = startTime.split(':');
      testDate.setHours(parseInt(hours), parseInt(minutes));
    }
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Asia/Kolkata',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    };
    return new Intl.DateTimeFormat('en-US', options).format(testDate);
  };

  const getStatusColor = (percentage: number): string => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-blue-600';
    if (percentage >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getGradeText = (percentage: number): string => {
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Average';
    if (percentage >= 40) return 'Poor';
    return 'Poor';
  };

  const filteredAndSortedSubmissions = submissions
    .filter(submission =>
      submission.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (submission.email && submission.email.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.studentName.localeCompare(b.studentName);
          break;
        case 'score':
          comparison = a.percentage - b.percentage;
          break;
        case 'time':
          comparison = a.timeSpent - b.timeSpent;
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen text-xl font-semibold text-primary bg-background">
        <svg className="animate-spin h-10 w-10 text-accent mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading Submissions...
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
        {/* Header */}
        <header className="relative mb-8">
          <button
            onClick={() => navigate('/teacher-dashboard')}
            className="absolute top-0 left-0 z-10 flex items-center gap-1 text-sm text-muted-foreground hover:text-accent transition-colors bg-card rounded-full py-2 px-4 shadow border border-border hover:border-accent"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            Back
          </button>
          <div className="text-center pt-12 sm:pt-0">
            <h1 className="text-3xl sm:text-4xl font-bold text-primary">Test Submissions</h1>
            <p className="mt-1 text-md text-muted-foreground">Review student submissions and performance</p>
          </div>
        </header>

        {/* Test Details */}
        <div className="bg-card rounded-xl shadow-sm border border-border/50 p-6 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-foreground mb-1">
                {testDetails?.subject} | {testDetails?.topic}
              </h2>
              <p className="text-muted-foreground text-sm">
                {testDetails?.numberOfQuestions} Questions • {testDetails?.testType[0].toUpperCase() + testDetails?.testType.slice(1)} • {formatDateTimeIST(testDetails?.testDate, testDetails?.startTime)}
              </p>
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Duration: {testDetails?.duration} min</span>
            </div>
          </div>
        </div>

        {/* Submissions Table */}
        <div className="bg-card rounded-xl shadow-sm border border-border/50">
          {/* Table Header */}
          <div className="p-6 border-b border-border">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-primary">
                  All Students ({submissions.length})
                </h3>
                <span className="text-sm text-muted-foreground">
                  Completed: {submissions.filter(s => s.status === 'completed').length} •
                  Not Attempted: {submissions.filter(s => s.status === 'not-attempted').length}
                </span>
              </div>

              <div className="flex items-center gap-4">
                <input
                  type="text"
                  placeholder="Search name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-accent focus:border-accent"
                />
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field as 'name' | 'score' | 'time');
                    setSortOrder(order as 'asc' | 'desc');
                  }}
                  className="px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-accent focus:border-accent"
                >
                  <option value="score-desc">Score (High to Low)</option>
                  <option value="score-asc">Score (Low to High)</option>
                  <option value="name-asc">Name (A to Z)</option>
                  <option value="name-desc">Name (Z to A)</option>
                  <option value="time-asc">Time (Low to High)</option>
                  <option value="time-desc">Time (High to Low)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Table Content */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50">
                <tr className="border-b border-border">
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Student name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Grade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Time Spent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Submitted / Timeout
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Details
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {filteredAndSortedSubmissions.map((submission) => (
                  <tr key={submission.studentId} className="hover:bg-muted/30 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-accent/10 flex items-center justify-center">
                            <span className="text-sm font-medium text-accent">
                              {submission.studentName.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-primary">
                            {submission.studentName}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-primary">
                        {submission.status === 'completed' ? (
                          <span className={getStatusColor(submission.percentage)}>
                            {submission.score}/{testDetails?.numberOfQuestions} ({submission.percentage}%)
                          </span>
                        ) : (
                          <span className="text-muted-foreground">Not attempted</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {submission.status === 'completed' ? (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${submission.percentage >= 80
                          ? 'bg-green-100 text-green-800'
                          : submission.percentage >= 60
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                          }`}>
                          {getGradeText(submission.percentage)}
                        </span>
                      ) : (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                          Not Attempted
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {submission.status === 'completed' ? formatTime(submission.timeSpent) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {submission.submittedAt ? formatDateTimeIST(submission.submittedAt) : 'Not submitted'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {submission.status === 'completed' ? (
                        <button
                          onClick={() => navigate('/test-results', {
                            state: { studentId: submission.studentId, testId: testId, userType: 'teacher' }
                          })}
                          className="text-accent hover:text-accent/80 font-medium"
                        >
                          See Detail
                        </button>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAndSortedSubmissions.length === 0 && (
            <div className="text-center py-8">
              <ExclamationCircleIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No submissions found matching your search.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default TestSubmissions;
