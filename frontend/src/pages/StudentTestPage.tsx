import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
// import { fetchQuestions, submitTest } from '../api';
import { ArrowLeftIcon, ArrowRightIcon, ForwardIcon, CheckIcon, ClockIcon, PrinterIcon } from '@heroicons/react/24/outline';
import ConfirmSubmitDialog from '../components/ConfirmSubmitDialog';
import { useQuizState } from '../hooks/useQuizState';
import QuestionNavigator from '../components/QuestionNavigator';
import Background from '../components/Background';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useUser } from '../contexts/userContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { renderLatexContent } from '../components/RenderLatexContent';
import { usePageRefresh } from '../hooks/usePageRefresh';
import { toast, ToastContainer } from 'react-toastify';
import { fetchWithCache } from '@/utils/cacheUtil';
import { generateQuestionPaperPDF } from '@/utils/generateQuestionPaperPdf';
import ThemeToggle from '@/components/ThemeToggle';

// Define the TestItem interface
interface TestItem {
    id: string;
    class: ClassDetails[];
    subject: string;
    topics: string;
    date: Date;
    numOfQuestions: number;
    duration: number;
    instructions: string;
    totalMarks: number;
    startTime: string;
    questions: Question[];
    active: boolean;
    teacherId: string;
}

interface ClassDetails {
    className: string;
    classStd: string;
    id: string; // MongoDB uses _id
}

interface Question {
    id: string; // MongoDB uses _id
    question: string;
    options: string[];
    images?: string[];
    topic: string;
    subtopics?: string;
}

interface Response {
    questionId: string;
    selectedAnswer: number | null;
    intuition: string;
    timeSpent: number;
}

const cleanQuestionText = (questionText: string): string => {
    if (!questionText) return '';
    return questionText.replace(/\boptions:\s*[\s\S]*$/i, '').trim();
};

const cleanOptionText = (optionText: string): string => {
    if (!optionText) return '';
    return optionText.replace(/^[A-Za-z][\)\.\:]\s*/, '');
}

const StudentTest: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const testData = location.state;
    const [testDetails, setTestDetails] = useState<TestItem | null>(null);

    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [responses, setResponses] = useState<Response[]>([]);
    const [showFeedback, setShowFeedback] = useState(false);
    const [showConfirmSubmitDialog, setShowConfirmSubmitDialog] = useState(false);
    const [shouldNavigateAway, setShouldNavigateAway] = useState(false);

    const [questions, setQuestions] = useState<Question[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const { updateQuestionState, getQuestionState, resetQuizState } = useQuizState(testDetails?.numOfQuestions || 0);
    const currentQuestion = questions[currentQuestionIndex];
    const { selectedAnswer, intuition } = getQuestionState(currentQuestion?.id);
    const [isAnswered, setIsAnswered] = useState(false);
    const [attemptedQuestions, setAttemptedQuestions] = useState(0);
    const { user, setUser } = useUser();

    const [isSubmitting, setIsSubmitting] = useState(false);

    // Add these new state variables for mobile detection
    const [isMobile, setIsMobile] = useState(false);
    const [mobileView, setMobileView] = useState<'question' | 'options'>('question');

    // Add this useEffect to detect mobile screens
    useEffect(() => {
        const checkMobile = () => {
            const isMobileDevice = window.innerWidth < 768;
            setIsMobile(isMobileDevice);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Timer states
    const [testDuration, setTestDuration] = useState<number | undefined>(); // Set test duration in minutes
    const [questionTimers, setQuestionTimers] = useState<{ [key: string]: number }>({});
    const [testTimeRemaining, setTestTimeRemaining] = useState(0); // Initialize to 0 temporarily
    const [testStartTime, setTestStartTime] = useState<string | null>(null); // Initialize as null
    const [isTestActive, setIsTestActive] = useState(true);

    // Function to convert test data to TestItem format
    const formatTestData = (test: any): TestItem => ({
        id: test._id,
        subject: test.subject,
        class: Array.isArray(test.class) ? test.class : [test.class], // Handle both array and single object
        topics: Array.isArray(test.topics) ? test.topics.join(', ') : test.topics,
        date: new Date(test.testDate),
        numOfQuestions: test.numberOfQuestions,
        duration: test.duration,
        instructions: test.testInstructions || '',
        totalMarks: test.totalMarks,
        startTime: test.startTime,
        questions: test.questions.map((q: any) => ({
            id: q._id,
            question: q.question,
            options: q.options,
            images: q.images || [],
            topic: q.topic,
            subtopic: q.subtopic
        })),
        active: test.active,
        teacherId: test.createdBy
    });

    // Format time remaining for display
    const formatTime = (totalSeconds: number) => {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };
    const axiosPrivate = useAxiosPrivate();

    useEffect(() => {
        loadQuestions();
    }, []);

    // Update the useEffect for the test timer
    useEffect(() => {
        if (!isTestActive || !testDuration || !testStartTime) return;

        const interval = setInterval(() => {
            const startTimeMs = new Date(testStartTime).getTime();
            const elapsedSeconds = Math.floor((Date.now() - startTimeMs) / 1000);
            const remaining = testDuration * 60 - elapsedSeconds;

            if (remaining <= 0) {
                clearInterval(interval);
                setTestTimeRemaining(0);
                setIsTestActive(false);
                handleConfirmSubmit();
            } else {
                setTestTimeRemaining(remaining);
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [isTestActive, testStartTime, testDuration]);

    //Question Timer
    useEffect(() => {
        if (!currentQuestion?.id || isAnswered) return;

        //initialize timer for the new question if not exists
        if (!questionTimers[currentQuestion.id]) {
            setQuestionTimers((prev) => ({ ...prev, [currentQuestion.id]: 0 }));
        }
        const interval = setInterval(() => {
            setQuestionTimers(prev => ({
                ...prev,
                [currentQuestion.id]: (prev[currentQuestion.id] || 0) + 1
            }));
        }, 1000);

        return () => clearInterval(interval);
    }, [currentQuestion?.id, isAnswered]);
    usePageRefresh();

    // const images = require.context('../images', false, /\.(png|jpg|jpeg|svg)$/);

    const handleQuestionSelect = (index: number) => {
        setCurrentQuestionIndex(index);
    };

    const questionNavigatorData = questions.map((q, index) => ({
        id: q.id,
        answered: responses.some(r => r.questionId === q.id),
        preview: renderLatexContent(cleanQuestionText(q.question)?.substring(0, 28) + "...")
    }));

    const getISTTimestamp = () => {
        return new Date().toLocaleString("en-US", {
            timeZone: "Asia/Kolkata",
            hour12: false, // Use 24-hour format
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    const loadQuestions = async () => {
        try {
            const endpoint = `/api/test/${testData.testId}`;

            const data = await fetchWithCache(axiosPrivate, endpoint);
            const test = formatTestData(data.test);
            setTestDetails(test);
            console.error("Test fetched:", data.test);
            // console.error("Questions fetched:", response.data.test.questions);
            setTestDuration(test?.duration);
            setTestStartTime(getISTTimestamp()); // Set start time AFTER loading data

            // Flatten and format the questions correctly
            const formattedQuestions = data.test.questions.flatMap((q: any) => ({
                id: q._id,
                question: q.question,
                options: q.options,
                images: q.images || [],
                topic: q.topic,
                subtopic: q.subtopic
            }));

            setQuestions(formattedQuestions);
        } catch (error) {
            console.log('Failed to fetch questions:', error);
        } finally {
            setLoading(false);
        }
    };


    useEffect(() => {
        const handleBeforeUnload = (e: BeforeUnloadEvent) => {
            if (!shouldNavigateAway && !isSubmitting) { // Add isSubmitting check
                e.preventDefault();
                e.returnValue = 'Are you sure you want to leave? Your test will be submitted automatically if you leave now.';
                return e.returnValue;
            }
        };

        const handlePopState = (e: PopStateEvent) => {
            if (!shouldNavigateAway && !isSubmitting) { // Add isSubmitting check
                e.preventDefault();
                setShowConfirmSubmitDialog(true);
                // Push the current state back to prevent navigation
                window.history.pushState(null, '', window.location.pathname);
            }
        };

        // Add event listeners
        window.addEventListener('beforeunload', handleBeforeUnload);
        window.addEventListener('popstate', handlePopState);

        // Prevent right-click context menu
        const handleContextMenu = (e: MouseEvent) => {
            e.preventDefault();
            return false;
        };
        document.addEventListener('contextmenu', handleContextMenu);

        // Push a new state to the history stack
        window.history.pushState(null, '', window.location.pathname);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
            window.removeEventListener('popstate', handlePopState);
            document.removeEventListener('contextmenu', handleContextMenu);
        };
    }, [shouldNavigateAway, isSubmitting]);

    // useEffect for existing response handling and moving to new question
    useEffect(() => {
        // Check if the current question has been answered before
        const existingResponse = responses.find(r => r.questionId === currentQuestion?.id);
        // console.error("current q id: ", currentQuestion.id);
        if (existingResponse) {
            setIsAnswered(true);
            setShowFeedback(true);
            updateQuestionState(currentQuestion?.id, {
                selectedAnswer: existingResponse.selectedAnswer,
                intuition: existingResponse.intuition
            });
        } else {
            setIsAnswered(false);
            setShowFeedback(false);
            updateQuestionState(currentQuestion?.id, { selectedAnswer: null, intuition: '' });
        }
        // console.error("currnt q: ", currentQuestion)
    }, [currentQuestionIndex, currentQuestion]);


    useEffect(() => {
        const attempted = responses.length;
        setAttemptedQuestions(attempted);
    }, [responses]);

    const handleAnswerSelection = (index: number) => {
        if (!isAnswered) {
            updateQuestionState(currentQuestion.id, { selectedAnswer: index, intuition });
            setShowFeedback(false);
        }
    };

    const handleIntuitionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        if (!isAnswered) {
            updateQuestionState(currentQuestion.id, { selectedAnswer, intuition: e.target.value });
        }
    };

    const markAnswer = () => {
        if (!isAnswered) {
            setShowFeedback(true);
            setIsAnswered(true);
            const newResponse: Response = {
                questionId: currentQuestion.id,
                selectedAnswer: selectedAnswer,
                intuition: intuition,
                timeSpent: questionTimers[currentQuestion.id] || 0
            };
            setResponses(prev => [...prev.filter(r => r.questionId !== currentQuestion.id), newResponse]);
        }
    };

    const handlePrevQuestion = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
    };

    const handleNextQuestion = () => {
        if (currentQuestionIndex < questions.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
            setShowFeedback(false);
            setIsAnswered(false);
        } else {
            handleSubmitTest();
        }
    };

    const skipQuestion = () => {
        handleNextQuestion();
    };

    const handleSubmitTest = () => {
        setShowConfirmSubmitDialog(true);
    };

    const handleConfirmSubmit = async () => {
        if (isSubmitting) return; // Prevent multiple submissions

        setIsSubmitting(true);
        setIsTestActive(false);

        // Show submitting toast - use loading type
        const submittingToast = toast.loading("Submitting your test...", {
            position: "top-center"
        });
        console.error("start time: ", testStartTime);

        try {
            const finalResponses = questions.map(question => {
                const existingResponse = responses.find(r => r.questionId === question.id);
                return {
                    questionId: question.id,
                    selectedAnswer: existingResponse?.selectedAnswer ?? null,
                    intuition: existingResponse?.intuition ?? '',
                    timeSpent: questionTimers[question.id] || 0,
                };
            });

            const response = await axiosPrivate.post('/api/test/submit', {
                userId: user?.id,
                testId: testData.testId,
                responses: finalResponses,
                startTime: testStartTime,
                endTime: getISTTimestamp(),
                subject: testDetails?.subject
            });

            sessionStorage.removeItem('quizProgress');
            sessionStorage.removeItem('practiceTestState');
            sessionStorage.removeItem('quizState');
            setShouldNavigateAway(true);

            // Update the submitting toast to success
            toast.update(submittingToast, {
                render: "Test submitted successfully!",
                type: "success",
                isLoading: false,
                autoClose: 2000
            });

            setTimeout(() => {
                setShowConfirmSubmitDialog(false); // Close dialog before navigation
                navigate('/test-results', { state: { responseData: response.data, studentId: user?.id, testId: testDetails?.id } });
            }, 2000);
        } catch (error) {
            console.log("Error submitting test:", error);

            // Update the submitting toast to error
            toast.update(submittingToast, {
                render: "Failed to submit test",
                type: "error",
                isLoading: false,
                autoClose: 2000
            });

            setIsSubmitting(false); // Re-enable buttons on error
            setIsTestActive(true); // Re-enable test if submission fails
        }
    };


    const [containerWidth, setContainerWidth] = useState('w-80');
    const prefix = 'https://aegisscholar-platform-web.s3.ap-south-1.amazonaws.com/uploads/images/questions/';

    const handlePrintPaper = () => {
        console.log("Printing question paper...");
        const classString = testDetails?.class?.[0]?.classStd || 'N/A';
        console.log("Class name being used:", classString);

        const testData = {
            username: user?.username || 'Student',
            topics: testDetails?.topics || '',
            class: classString,
            subject: testDetails?.subject || 'Test',
            date: new Date(testDetails?.date ?? new Date()).toLocaleDateString('en-IN'),
            duration: testDetails?.duration || 0,
            instructions: testDetails?.instructions || '',
            totalMarks: testDetails?.totalMarks || 0
        };

        generateQuestionPaperPDF(testData, questions);
    };

    return (
        <div className="flex flex-col h-screen overflow-hidden">
            <ToastContainer
                position="bottom-right"
                autoClose={2000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
            />
            {loading ? (
                // Show loading spinner while loading
                <div className="flex items-center justify-center h-screen">
                    <LoadingSpinner />
                </div>
            ) : (
                // Show main content when not loading
                <>
                    {/* <Background /> */}

                    {/* Mobile Header - Only visible on mobile */}
                    {isMobile && (
                        <div className="bg-card shadow-md p-3 z-10 flex justify-between items-center border-b border-border">
                            <div className="flex items-center gap-2">
                                <QuestionNavigator
                                    questions={questionNavigatorData}
                                    currentQuestionIndex={currentQuestionIndex}
                                    onQuestionSelect={handleQuestionSelect}
                                />
                                <h1 className="text-base font-semibold text-primary">{testDetails?.subject || 'Test'}</h1>
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-accent-light text-accent text-xs font-medium">
                                    {currentQuestionIndex + 1}/{questions.length}
                                </span>
                            </div>
                            <div className="flex items-center gap-2">
                                <ClockIcon className="h-4 w-4 text-foreground" />
                                <span className="text-sm text-foreground font-medium">
                                    {formatTime(testStartTime ? testTimeRemaining : 0)}
                                </span>
                            </div>
                        </div>
                    )}

                    {/* Desktop Layout */}
                    {!isMobile && (
                        <main className="grow flex items-start justify-between p-4 my-4 space-x-4 overflow-hidden h-full w-full">
                            <QuestionNavigator
                                questions={questionNavigatorData}
                                currentQuestionIndex={currentQuestionIndex}
                                onQuestionSelect={handleQuestionSelect}
                                onExpandChange={(expanded) => {
                                    setContainerWidth(expanded ? 'w-1/3' : 'w-12');
                                }}
                            />

                            <div className="bg-card rounded-lg shadow-lg grow h-[85vh] w-1/2 overflow-auto p-6 border border-border">
                                <div className="flex items-center bg-secondary rounded-full py-1 mb-4 justify-between">
                                    <span className="text-sm font-medium text-primary px-2">
                                        Question {currentQuestionIndex + 1} of {questions.length}
                                    </span>
                                </div>

                                <h2 className="text-xl sm:text-2xl font-semibold mb-6 text-card-foreground leading-relaxed">
                                    {renderLatexContent(cleanQuestionText(currentQuestion?.question))}
                                </h2>

                                {currentQuestion?.images &&
                                    currentQuestion.images
                                        .filter((image) => !image.includes("_a"))
                                        .map((image, index) => {
                                            const imageName = image.split("/").pop();
                                            const imagePath = `${prefix}${imageName}`;
                                            return (
                                                <img
                                                    key={index}
                                                    src={imagePath}
                                                    className="w-full rounded-lg shadow-md mb-6"
                                                    alt={`Question Image ${index + 1}`}
                                                />
                                            );
                                        })}
                            </div>

                            <div className="bg-card rounded-lg shadow-lg grow h-[85vh] w-1/3 overflow-auto p-6 border border-border">
                                <div className="space-y-4 mb-6">
                                    {currentQuestion?.options?.map((option, index) => (
                                        <label
                                            key={index}
                                            className={`flex items-center p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer
                                            ${selectedAnswer === index ? "border-accent border-2 bg-accent-light" : "border-border hover:border-accent-light"}
                                            ${isAnswered ? "cursor-not-allowed" : ""}`}
                                        >
                                            <input
                                                type="radio"
                                                name="quiz-option"
                                                value={index}
                                                checked={selectedAnswer === index}
                                                onChange={() => handleAnswerSelection(index)}
                                                disabled={isAnswered}
                                                className="h-4 w-4 text-accent mr-3"
                                            />
                                            <span>{renderLatexContent(cleanOptionText(option))}</span>
                                        </label>
                                    ))}
                                </div>

                                <div className="mt-6 grow">
                                    <label className="block text-sm font-medium text-card-foreground mb-2">
                                        Your Intuition/Approach:
                                    </label>
                                    <textarea
                                        value={intuition}
                                        onChange={handleIntuitionChange}
                                        disabled={isAnswered}
                                        className="w-full h-32 p-3 border border-border rounded-lg shadow-sm focus:ring-1 focus:ring-primary
                                        disabled:bg-muted disabled:cursor-not-allowed resize-none bg-background text-foreground"
                                        placeholder="Explain your approach or thought process..."
                                    />
                                </div>
                            </div>
                        </main>
                    )}

                    {/* Mobile Layout */}
                    {isMobile && (
                        <main className="grow overflow-auto p-3 pb-16">
                            <div className="bg-card rounded-lg shadow p-4 border border-border">
                                {/* Question Section */}
                                <div className="mb-5">
                                    <h2 className="text-base sm:text-lg font-semibold mb-4 text-card-foreground leading-relaxed">
                                        {renderLatexContent(cleanQuestionText(currentQuestion?.question))}
                                    </h2>

                                    {currentQuestion?.images &&
                                        currentQuestion.images
                                            .filter((image) => !image.includes("_a"))
                                            .map((image, index) => {
                                                const imageName = image.split("/").pop();
                                                const imagePath = `${prefix}${imageName}`;
                                                return (
                                                    <img
                                                        key={index}
                                                        src={imagePath}
                                                        className="w-full rounded-lg shadow-md mb-4"
                                                        alt={`Question Image ${index + 1}`}
                                                    />
                                                );
                                            })}
                                </div>

                                {/* Divider */}
                                <div className="border-t border-border my-4"></div>

                                {/* Options Section */}
                                <div className="mb-5">
                                    {/* <h3 className="text-sm font-semibold text-gray-700 mb-3">Select your answer:</h3> */}
                                    <div className="space-y-3">
                                        {currentQuestion.options.map((option, index) => (
                                            <label
                                                key={index}
                                                className={`flex items-center p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer
                            ${selectedAnswer === index ? "border-accent bg-accent-light" : "border-border"}
                            ${isAnswered ? "cursor-not-allowed" : ""}`}
                                            >
                                                <input
                                                    type="radio"
                                                    name="quiz-option"
                                                    value={index}
                                                    checked={selectedAnswer === index}
                                                    onChange={() => handleAnswerSelection(index)}
                                                    disabled={isAnswered}
                                                    className="h-4 w-4 text-accent mr-3"
                                                />
                                                <span>{renderLatexContent(cleanOptionText(option))}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>

                                {/* Intuition/Approach Section
                                <div className="mb-6">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Your Intuition/Approach:
                                    </label>
                                    <textarea
                                        value={intuition}
                                        onChange={handleIntuitionChange}
                                        disabled={isAnswered}
                                        className="w-full h-24 p-3 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary 
                    disabled:bg-gray-50 disabled:cursor-not-allowed resize-none"
                                        placeholder="Explain your approach or thought process..."
                                    />
                                </div> */}

                                {/* Navigation Controls */}
                                <div className="flex flex-wrap justify-between mt-6 gap-2">
                                    <div className="flex gap-2 w-full">
                                        {currentQuestionIndex > 0 && (
                                            <button
                                                onClick={handlePrevQuestion}
                                                className="flex items-center justify-center px-3 py-2 text-accent hover:bg-accent-light rounded-lg shadow-sm"
                                                aria-label="Previous question"
                                            >
                                                <ArrowLeftIcon className="h-5 w-5" />
                                                <span className="sr-only">Previous</span>
                                            </button>
                                        )}

                                        <button
                                            onClick={() => {
                                                if (!showFeedback) {
                                                    markAnswer();
                                                }
                                                handleNextQuestion();
                                            }}
                                            disabled={selectedAnswer === null}
                                            className="flex-grow flex items-center justify-center px-4 py-2 bg-accent text-accent-foreground rounded-lg
                disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed transition-all duration-200"
                                        >
                                            {currentQuestionIndex === questions.length - 1 ? "Save & Submit" : "Save & Next"}
                                            <CheckIcon className="ml-2 h-5 w-5" />
                                        </button>

                                        {!isAnswered && currentQuestionIndex < questions.length - 1 && (
                                            <button
                                                onClick={skipQuestion}
                                                className="flex items-center justify-center px-3 py-2 text-accent hover:bg-accent-light rounded-lg shadow-sm"
                                                aria-label="Skip question"
                                            >
                                                <ForwardIcon className="h-5 w-5" />
                                                <span className="sr-only">Skip</span>
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </main>
                    )}

                    {/* Desktop Bottom Bar */}
                    {!isMobile && (
                        <div className="bg-card border-t shadow-lg p-4 mt-auto h-[80px] border border-border">
                            <div className="flex items-center justify-between max-w-full mx-auto gap-6">
                                {/* Left Side - Timer */}
                                <div className="flex items-center rounded-xl border border-border-200 p-2 bg-muted space-x-2">
                                    <ClockIcon className="h-5 w-5 text-foreground" />
                                    <span className="text-foreground font-medium ml-2">
                                        {formatTime(testStartTime ? testTimeRemaining : 0)}
                                    </span>
                                </div>

                                {/* Center - Navigation Buttons */}
                                <div className="flex items-center space-x-4">
                                    {currentQuestionIndex > 0 && (
                                        <button
                                            onClick={handlePrevQuestion}
                                            className="flex items-center px-4 py-2 hover:cursor-pointer text-accent hover:bg-accent-light rounded-lg"
                                        >
                                            <ArrowLeftIcon className="mr-2 h-5 w-5" />
                                            Previous
                                        </button>
                                    )}

                                    <button
                                        onClick={() => {
                                            if (!showFeedback) {
                                                markAnswer();
                                            }
                                            handleNextQuestion();
                                        }}
                                        disabled={selectedAnswer === null}
                                        className="flex items-center hover:cursor-pointer justify-center px-4 py-2 bg-accent text-accent-foreground rounded-lg
                disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
                                    >
                                        {currentQuestionIndex === questions.length - 1 ? "Save & Submit" : "Save & Next"}
                                        <CheckIcon className="ml-2 h-5 w-5" />
                                    </button>

                                    {!isAnswered && currentQuestionIndex < questions.length - 1 && (
                                        <button
                                            onClick={skipQuestion}
                                            className="flex items-center hover:cursor-pointer px-4 py-2 text-accent hover:bg-accent-light rounded-lg"
                                        >
                                            Skip
                                            <ForwardIcon className="ml-2 h-5 w-5" />
                                        </button>
                                    )}
                                </div>
                                {/* Right Side - Utility Buttons */}
                                <div className='flex items-center space-x-6'>
                                    <div className="hidden md:block">
                                        <ThemeToggle size="default" />
                                    </div>
                                    <button
                                        onClick={handlePrintPaper}
                                        className="flex items-center hover:cursor-pointer px-4 py-2 text-accent hover:bg-accent-light rounded-lg">
                                        <PrinterIcon className="mr-2 h-5 w-5" />
                                        Print Paper
                                    </button>
                                    <button
                                        onClick={handleSubmitTest}
                                        disabled={isSubmitting}
                                        className="flex items-center hover:cursor-pointer px-6 py-2 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-all duration-200 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
                                    >
                                        {isSubmitting ? "Submitting..." : "Submit"}
                                        <CheckIcon className="ml-2 h-5 w-5" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Mobile Bottom Bar */}
                    {isMobile && (
                        <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border shadow-lg z-10">
                            {/* Progress bar */}
                            <div className="w-full bg-muted h-1">
                                <div
                                    className="bg-accent h-1 transition-all duration-300"
                                    style={{ width: `${(attemptedQuestions / questions.length) * 100}%` }}
                                ></div>
                            </div>

                            <div className="flex justify-end items-center p-2">
                                <div className="flex justify-center items-center p-2">
                                    <ThemeToggle size="default" />
                                </div>
                                <button
                                    onClick={handleSubmitTest}
                                    disabled={isSubmitting}
                                    className="flex items-center hover:cursor-pointer px-6 py-2 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-all duration-200 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
                                >
                                    {isSubmitting ? "Submitting..." : "Submit"}
                                    <CheckIcon className="ml-2 h-5 w-5" />
                                </button>
                            </div>
                        </div>
                    )}
                </>
            )}

            {/* Submit Confirmation Dialog */}
            <ConfirmSubmitDialog
                show={showConfirmSubmitDialog}
                onConfirm={() => {
                    setShouldNavigateAway(false);
                    setShowConfirmSubmitDialog(true);
                    handleConfirmSubmit();
                }}
                onCancel={() => setShowConfirmSubmitDialog(false)}
                attemptedQuestions={attemptedQuestions}
                totalQuestions={questions.length}
                isSubmitting={isSubmitting} // Add this prop
            />
        </div>
    );
};

export default StudentTest;