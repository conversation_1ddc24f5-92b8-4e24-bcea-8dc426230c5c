import { useState, useEffect } from 'react';
import { useUser } from '../contexts/userContext';
import AegisAiChatbot from '@/components/AegisAiChatbot';
import TeacherAegisAi from '@/components/TeacherAegisAi';
import { AlertCircle } from 'lucide-react';

const AegisAi = () => {
    const { user } = useUser();
    const [selectedSubject, setSelectedSubject] = useState<string | null>(null);

    // If user has only one subject, select it by default (for students)
    useEffect(() => {
        if (user?.role === 'Student') {
            if (user?.subjects?.length === 1) {
                setSelectedSubject(user.subjects[0].subjectName);
            } else if (user?.subjects && user.subjects.length > 1 && !selectedSubject) {
                // Auto-select the first subject if none is selected
                setSelectedSubject(user.subjects[0].subjectName);
            }
        }
    }, [user, selectedSubject]);

    // Render teacher interface for teachers
    if (user?.role === 'Teacher') {
        return <TeacherAegisAi />;
    }

    // Render student interface for students (existing functionality)
    return (
        <div className="h-screen bg-background overflow-hidden">
            {/* Mobile-first layout with minimal header */}
            <div className="h-full flex flex-col">
                {/* Show error message if no subjects */}
                {!user?.subjects?.length && (
                    <div className="flex-shrink-0 px-4 py-6 border-b border-border bg-gradient-to-r from-primary-100 to-primary-200 dark:from-primary-900/40 dark:to-primary-950/30 flex items-center justify-center">
                        <div className="flex items-center gap-3">
                            <AlertCircle className="text-warning-500 dark:text-warning-400" />
                            <div>
                                <p className="text-base font-semibold text-warning-800 dark:text-warning-100 mb-1">
                                    No subjects found
                                </p>
                                <p className="text-sm text-warning-700 dark:text-warning-200">
                                    Please contact your instructor or&nbsp;
                                    <a
                                        href="/student-dashboard"
                                        className="underline text-warning-800 dark:text-warning-100 hover:text-warning-600"
                                    >
                                        join a class from Dashboard
                                    </a>
                                    .
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Main chat area - full height */}
                {selectedSubject && user?.id && (
                    <div className="flex-1 overflow-hidden">
                        <AegisAiChatbot
                            subject={selectedSubject}
                            studentId={user.id}
                            availableSubjects={user.subjects || []}
                            onSubjectChange={setSelectedSubject}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default AegisAi;