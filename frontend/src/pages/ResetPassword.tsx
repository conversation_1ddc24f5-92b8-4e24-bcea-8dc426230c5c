import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { LockClosedIcon, EyeIcon, EyeSlashIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import Background from '../components/Background';
import { ToastContainer, toast } from 'react-toastify';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';

const ResetPassword = () => {
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordType, setPasswordType] = useState('password');
    const [confirmPasswordType, setConfirmPasswordType] = useState('password');
    const [isValid, setIsValid] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isSuccess, setIsSuccess] = useState(false);
    const [error, setError] = useState('');
    const navigate = useNavigate();
    const axiosPrivate = useAxiosPrivate();

    // Get token from URL
    const [searchParams] = useSearchParams();
    const token = searchParams.get('token');
    const email = searchParams.get('email');

    // Password validation
    const [passwordStrength, setPasswordStrength] = useState({
        length: false,
        hasUpperCase: false,
        hasLowerCase: false,
        hasNumber: false,
        hasSpecialChar: false
    });

    useEffect(() => {
        const validateToken = async () => {
            try {
                // Instead of making a GET request, we'll make a POST with an empty password
                // The controller will still validate the token but not process a password reset
                await axiosPrivate.post(`/api/password/reset-password?token=${token}`, {
                    validateOnly: true  // Add this flag to indicate token validation only
                });

                setIsValid(true);
                setIsLoading(false);
            } catch (err) {
                setIsValid(false);
                setError('Invalid or expired reset link');
                setIsLoading(false);
            }
        };

        if (token) {
            validateToken();
        } else {
            setIsValid(false);
            setError('Missing reset token');
            setIsLoading(false);
        }
    }, [token, axiosPrivate]);

    useEffect(() => {
        setPasswordStrength({
            length: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumber: /[0-9]/.test(password),
            hasSpecialChar: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)
        });
    }, [password]);

    const togglePasswordType = (field: 'password' | 'confirm') => {
        if (field === 'password') {
            setPasswordType(passwordType === 'password' ? 'text' : 'password');
        } else {
            setConfirmPasswordType(confirmPasswordType === 'password' ? 'text' : 'password');
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        // Check if passwords match
        if (password !== confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        // Check if password meets all criteria
        const meetsAllCriteria = Object.values(passwordStrength).every(value => value);
        if (!meetsAllCriteria) {
            setError('Password does not meet all requirements');
            return;
        }

        setError('');
        const resetToast = toast.loading("Resetting password...", {
            position: "top-right",
            toastId: 'reset',
        });

        try {
            await axiosPrivate.post(`/api/password/reset-password?token=${token}`, {
                password: password
            });

            toast.update(resetToast, {
                render: "Password reset successful",
                type: "success",
                isLoading: false,
                autoClose: 1500,
            });

            setIsSuccess(true);
            setTimeout(() => navigate('/login'), 3000);
        } catch (err) {
            toast.update(resetToast, {
                render: "Failed to reset password",
                type: "error",
                isLoading: false,
                autoClose: 1500,
            });
            setError('Failed to reset password. Please try again.');
        }
    };

    return (
        <div className="min-h-screen flex flex-col relative overflow-hidden">
            <ToastContainer
                position="top-center"
                autoClose={1500}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
            />

            {/* Main Content */}
            <div className="grow flex items-center justify-center p-4 bg-background">
                <div className="backdrop-blur-sm bg-card border border-border p-8 rounded-2xl w-full max-w-md shadow-lg">
                    <AegisScholarLogoWithoutText className="w-16 h-16 mx-auto text-accent" />

                    {isLoading ? (
                        <div className="text-center p-8">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto"></div>
                            <p className="mt-4 text-muted-foreground">Verifying your reset link...</p>
                        </div>
                    ) : !isValid ? (
                        <div className="text-center p-8">
                            <ExclamationTriangleIcon className="mx-auto text-warning mb-4 w-12 h-12" />
                            <h2 className="text-2xl font-bold text-primary mb-2">Invalid Reset Link</h2>
                            <p className="text-muted-foreground mb-6">{error}</p>
                            <button
                                onClick={() => navigate('/login')}
                                className="w-full py-3 bg-accent text-white rounded-full hover:bg-accent-dark
                                         transition-all duration-300 cursor-pointer"
                            >
                                Back to Login
                            </button>
                        </div>
                    ) : isSuccess ? (
                        <div className="text-center p-8">
                            <CheckIcon className="mx-auto text-success mb-4 w-12 h-12" />
                            <h2 className="text-2xl font-bold text-primary mb-2">Password Reset Successful</h2>
                            <p className="text-muted-foreground mb-6">Your password has been successfully reset. You'll be redirected to the login page shortly.</p>
                        </div>
                    ) : (
                        <>
                            <div className="text-center mb-8 mt-4">
                                <h1 className="text-3xl font-bold text-primary mb-2">
                                    Create New Password
                                </h1>
                                <p className="text-muted-foreground">
                                    {email ? `For ${email}` : 'Set a new password for your account'}
                                </p>
                            </div>

                            {error && (
                                <div className="mb-6 p-3 rounded-lg bg-danger-light text-danger text-center">
                                    {error}
                                </div>
                            )}

                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-primary mb-2">
                                        New Password
                                    </label>
                                    <div className="relative">
                                        <input
                                            type={passwordType}
                                            required
                                            className="w-full px-4 py-2 pl-10 bg-secondary-100 border border-border rounded-lg
                                                     focus:outline-none focus:ring-2 focus:ring-accent-light transition-all text-primary"
                                            placeholder="Enter new password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                        />
                                        <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-[18px] h-[18px]" />
                                        {passwordType !== "password" ?
                                            <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground cursor-pointer w-[18px] h-[18px]"
                                                onClick={() => togglePasswordType('password')} /> :
                                            <EyeSlashIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground cursor-pointer w-[18px] h-[18px]"
                                                onClick={() => togglePasswordType('password')} />
                                        }
                                    </div>
                                </div>

                                {/* Password requirements */}
                                <div className="bg-secondary-100 p-3 rounded-lg">
                                    <p className="text-sm font-medium text-primary mb-2">Password must have:</p>
                                    <ul className="space-y-1">
                                        <li className={`text-sm flex items-center ${passwordStrength.length ? 'text-success' : 'text-muted-foreground'}`}>
                                            <span className={`mr-2 inline-block w-4 h-4 rounded-full ${passwordStrength.length ? 'bg-success-light text-success' : 'bg-gray-200'} flex items-center justify-center`}>
                                                {passwordStrength.length && <CheckIcon className="w-3 h-3" />}
                                            </span>
                                            At least 8 characters
                                        </li>
                                        <li className={`text-sm flex items-center ${passwordStrength.hasUpperCase ? 'text-success' : 'text-muted-foreground'}`}>
                                            <span className={`mr-2 inline-block w-4 h-4 rounded-full ${passwordStrength.hasUpperCase ? 'bg-success-light text-success' : 'bg-gray-200'} flex items-center justify-center`}>
                                                {passwordStrength.hasUpperCase && <CheckIcon className="w-3 h-3" />}
                                            </span>
                                            At least one uppercase letter
                                        </li>
                                        <li className={`text-sm flex items-center ${passwordStrength.hasLowerCase ? 'text-success' : 'text-muted-foreground'}`}>
                                            <span className={`mr-2 inline-block w-4 h-4 rounded-full ${passwordStrength.hasLowerCase ? 'bg-success-light text-success' : 'bg-gray-200'} flex items-center justify-center`}>
                                                {passwordStrength.hasLowerCase && <CheckIcon className="w-3 h-3" />}
                                            </span>
                                            At least one lowercase letter
                                        </li>
                                        <li className={`text-sm flex items-center ${passwordStrength.hasNumber ? 'text-success' : 'text-muted-foreground'}`}>
                                            <span className={`mr-2 inline-block w-4 h-4 rounded-full ${passwordStrength.hasNumber ? 'bg-success-light text-success' : 'bg-gray-200'} flex items-center justify-center`}>
                                                {passwordStrength.hasNumber && <CheckIcon className="w-3 h-3" />}
                                            </span>
                                            At least one number
                                        </li>
                                        <li className={`text-sm flex items-center ${passwordStrength.hasSpecialChar ? 'text-success' : 'text-muted-foreground'}`}>
                                            <span className={`mr-2 inline-block w-4 h-4 rounded-full ${passwordStrength.hasSpecialChar ? 'bg-success-light text-success' : 'bg-gray-200'} flex items-center justify-center`}>
                                                {passwordStrength.hasSpecialChar && <CheckIcon className="w-3 h-3" />}
                                            </span>
                                            At least one special character
                                        </li>
                                    </ul>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-primary mb-2">
                                        Confirm New Password
                                    </label>
                                    <div className="relative">
                                        <input
                                            type={confirmPasswordType}
                                            required
                                            className="w-full px-4 py-2 pl-10 bg-secondary-100 border border-border rounded-lg
                                                     focus:outline-none focus:ring-2 focus:ring-accent-light transition-all text-primary"
                                            placeholder="Confirm new password"
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                        />
                                        <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-[18px] h-[18px]" />
                                        {confirmPasswordType !== "password" ?
                                            <EyeIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground cursor-pointer w-[18px] h-[18px]"
                                                onClick={() => togglePasswordType('confirm')} /> :
                                            <EyeSlashIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground cursor-pointer w-[18px] h-[18px]"
                                                onClick={() => togglePasswordType('confirm')} />
                                        }
                                    </div>
                                    {password && confirmPassword && password !== confirmPassword && (
                                        <p className="mt-1 text-sm text-danger">Passwords do not match</p>
                                    )}
                                </div>

                                <button
                                    type="submit"
                                    className="w-full py-3 bg-accent text-white rounded-full hover:bg-accent-dark
                                             transition-all duration-300 flex items-center justify-center cursor-pointer"
                                >
                                    Reset Password
                                </button>
                            </form>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;