import {
  ReactFlow,
  Controls,
  Panel,
  MiniMap,
  Edge,
  FitViewOptions,
  OnNodesChange,
  applyNodeChanges,
  ColorMode,
  ReactFlowProvider,
  OnEdgesChange,
  applyEdgeChanges,
  MarkerType,
  ConnectionLineType,
  SmoothStepEdge,
  useReactFlow,
  NodeMouseHandler
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { SetStateAction, useCallback, useEffect, useMemo, useState, useRef } from 'react';
import Dagre from '@dagrejs/dagre';
import { nodeTypes } from '../components/CustomNodes/index';
import { CustomNode } from '../components/CustomNodes/types';
import Modal from 'react-modal';
import { X } from 'lucide-react';

const defaultViewport = { x: 0, y: 0, zoom: 2 };

const edgeTypes = {
  'smoothStep': SmoothStepEdge,
};

const EDGE_COLORS = {
  SUBJECT_TO_CHAPTER: {
    stroke: 'hsl(var(--graph-edge-primary))',
    marker: 'hsl(var(--primary))'
  },
  CHAPTER_TO_SUBTOPIC: {
    stroke: 'hsl(var(--graph-edge-secondary))',
    marker: 'hsl(var(--success))'
  },
  SUBTOPIC_TO_SUBTOPIC: {
    stroke: 'hsl(var(--graph-edge-tertiary))',
    marker: 'hsl(var(--warning))'
  }
};

// Dagre layout function
const getLayoutedElements = (nodes: CustomNode[], edges: Edge[]) => {
  const dagreGraph = new Dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({
    rankdir: 'TB',  // top-to-bottom layout
    align: '',    // upstream left alignment
    nodesep: 100,   // horizontal spacing between nodes
    ranksep: 100,   // vertical spacing between layers
    edgesep: 50
  });

  // Add nodes to the graph
  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { width: 250, height: 100 });
  });

  // Add edges to the graph
  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // Calculate the layout
  Dagre.layout(dagreGraph);

  // Update node positions based on layout
  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);
    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWithPosition.width / 2,
        y: nodeWithPosition.y - nodeWithPosition.height / 2,
      },
    };
  });

  return { nodes: layoutedNodes, edges };
};

const SUBJECT_DESCRIPTIONS: Record<string, string> = {
  mathematics: 'Mathematics is the study of numbers, quantities, and shapes. It is a fundamental subject that is used in everyday life and is essential for many careers.',
  science: 'Science is the study of the natural world and how it works. It is a subject that helps us understand the world around us and make informed decisions.',
  english: 'English is the study of language, literature, and communication. It is a subject that helps us express ourselves and understand the world.',
  hindi: 'Hindi is the study of the Hindi language and its literature. It is an important subject that helps us connect with our culture and heritage.',
  "social science": 'Social Science is the study of human society and the relationships between individuals and groups. It is a subject that helps us understand the world and our place in it.',
};

// Define props interface
interface SubjectDetailsGraphProps {
  graphData: any; // Consider defining a more specific type for graphData
}

// Main component without ReactFlowProvider wrapper
const SubjectDetailsGraph: React.FC<SubjectDetailsGraphProps> = ({ graphData }) => {
  const [selectedNode, setSelectedNode] = useState<CustomNode | null>(null);
  const [nodes, setNodes] = useState<Array<CustomNode>>([]);
  const [edges, setEdges] = useState<Array<Edge>>([]);
  const [colorMode, setColorMode] = useState<ColorMode>('light');

  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // Store initial viewport for reset
  const initialViewportRef = useRef<{ x: number; y: number; zoom: number } | null>(null);
  const [hasInitialViewport, setHasInitialViewport] = useState(false);

  const { fitView, getViewport, setViewport } = useReactFlow();

  // Handle search input change
  const handleSearchChange = useCallback((event: { target: { value: SetStateAction<string>; }; }) => {
    setSearchQuery(event.target.value);
  }, []);

  // Debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Store initial viewport when nodes are first loaded
  useEffect(() => {
    if (nodes.length > 0 && !hasInitialViewport) {
      // Wait for initial fitView to complete, then store the viewport
      setTimeout(() => {
        const viewport = getViewport();
        initialViewportRef.current = viewport;
        setHasInitialViewport(true);
      }, 500); // Wait for initial fitView animation
    }
  }, [nodes, hasInitialViewport, getViewport]);

  // Filtered nodes with search highlighting
  const filteredNodes = useMemo(() => {
    const searchLower = debouncedSearchQuery.toLowerCase().trim();

    if (!searchLower) {
      // No search query - return nodes with default styling
      return nodes.map((node) => ({
        ...node,
        style: {
          border: '1px solid hsl(var(--border))',
          backgroundColor: node.type === 'subject' ? 'hsl(var(--muted))' : 'hsl(var(--background))',
        },
      }));
    }

    // When searching, highlight matches and dim non-matches
    return nodes.map((node) => {
      const nameMatch = node.data.name.toLowerCase().includes(searchLower);
      const descriptionMatch = (node.data.description ?? '').toLowerCase().includes(searchLower);
      const isMatch = nameMatch || descriptionMatch;

      return {
        ...node,
        style: {
          border: isMatch ? '2px solid hsl(var(--primary))' : '1px solid hsl(var(--border))',
          backgroundColor: isMatch
            ? 'hsl(var(--primary) / 0.1)'
            : node.type === 'subject'
              ? 'hsl(var(--muted))'
              : 'hsl(var(--background))',
          opacity: isMatch ? 1 : 0.4,
          boxShadow: isMatch ? '0 2px 8px hsl(var(--primary) / 0.2)' : 'none',
        },
      };
    });
  }, [nodes, debouncedSearchQuery]);

  // Enhanced search zoom effect
  useEffect(() => {
    if (debouncedSearchQuery.trim()) {
      const searchLower = debouncedSearchQuery.toLowerCase().trim();

      // Find matching nodes
      const matchingNodes = nodes.filter((node) => {
        const nameMatch = node.data.name.toLowerCase().includes(searchLower);
        const descriptionMatch = (node.data.description ?? '').toLowerCase().includes(searchLower);
        return nameMatch || descriptionMatch;
      });

      if (matchingNodes.length > 0) {
        // Use setTimeout to ensure filteredNodes are rendered before fitView
        setTimeout(() => {
          if (matchingNodes.length === 1) {
            // Single match - zoom in close
            fitView({
              nodes: matchingNodes.map(node => ({ id: node.id })),
              padding: 0.8,
              duration: 600,
              minZoom: 1.5,
              maxZoom: 3
            });
          } else {
            // Multiple matches - find the "most relevant" one
            // Priority: exact match > starts with > contains
            const exactMatches = matchingNodes.filter(node =>
              node.data.name.toLowerCase() === searchLower
            );
            const startsWithMatches = matchingNodes.filter(node =>
              node.data.name.toLowerCase().startsWith(searchLower)
            );

            let primaryMatch;
            if (exactMatches.length > 0) {
              primaryMatch = exactMatches[0];
            } else if (startsWithMatches.length > 0) {
              primaryMatch = startsWithMatches[0];
            } else {
              primaryMatch = matchingNodes[0];
            }

            // Zoom to primary match first, then fit all matches
            fitView({
              nodes: [{ id: primaryMatch.id }],
              padding: 1.2,
              duration: 400,
              minZoom: 1.2,
              maxZoom: 2.5
            });

            // Then adjust to show all matches if there are many
            if (matchingNodes.length > 1) {
              setTimeout(() => {
                fitView({
                  nodes: matchingNodes.map(node => ({ id: node.id })),
                  padding: 0.8,
                  duration: 300,
                  minZoom: 0.5,
                  maxZoom: 2
                });
              }, 500);
            }
          }
        }, 100);
      }
    }
  }, [debouncedSearchQuery, nodes, fitView]);

  // Add useEffect to process graphData prop when it changes
  useEffect(() => {
    if (graphData) {
      const { nodes: transformedNodes, edges: transformedEdges } = transformGraphData(graphData);
      const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(transformedNodes, transformedEdges);
      setNodes(layoutedNodes);
      setEdges(layoutedEdges);
      setHasInitialViewport(false); // Reset viewport tracking for new data
    } else {
      setNodes([]);
      setEdges([]);
      setHasInitialViewport(false);
    }
  }, [graphData]);

  // Function to transform MongoDB data into React Flow nodes and edges
  const transformGraphData = (data: any): { nodes: CustomNode[]; edges: Edge[] } => {
    const curriculumNodes = data.curriculumProgress || [];

    const topicIds = curriculumNodes
      .filter((currNode: any) => currNode.nodeId.type === "Chapter")
      .map((topic: any) => topic.nodeId._id);

    const subjectNode = {
      id: `subject-${data.subject}`,
      type: 'subject',
      data: {
        name: data.subject,
        description: SUBJECT_DESCRIPTIONS[data.subject.toLowerCase()] || 'No description available',
        topics: topicIds,
        isExpanded: true,
      },
      position: { x: 0, y: 0 },
    };

    const nodes = curriculumNodes.map((currNode: any) => {
      const { nodeId, proficiency, status } = currNode;
      return {
        id: nodeId._id,
        type: nodeId.type === "Chapter" ? "chapter" : "subtopic",
        data: {
          name: nodeId.name,
          description: nodeId.description,
          order: nodeId.order,
          children: nodeId.children.map((child: any) => child._id),
          parents: nodeId.parents.map((parent: any) => parent._id),
          proficiency: proficiency || 0,
          status: status || "Not Started",
          isExpanded: false,
        },
        position: { x: 0, y: 0 },
      };
    });

    nodes.unshift(subjectNode);

    const edges = [
      ...curriculumNodes.flatMap((currNode: any, index: any) =>
        currNode.nodeId.children.map((child: any, childIndex: any) => {
          const isSubtopicToSubtopic =
            currNode.nodeId.type === "Subtopic" &&
            curriculumNodes.find((n: { nodeId: { _id: any; }; }) => n.nodeId._id === child)?.nodeId.type === "Subtopic";

          const colors = isSubtopicToSubtopic
            ? EDGE_COLORS.SUBTOPIC_TO_SUBTOPIC
            : EDGE_COLORS.CHAPTER_TO_SUBTOPIC;

          return {
            id: `edge-${currNode.nodeId._id}-${child}-${index}-${childIndex}`,
            source: currNode.nodeId._id,
            target: child,
            type: 'smoothStep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 20,
              height: 20,
              color: colors.marker,
            },
            style: {
              strokeWidth: 2,
              stroke: colors.stroke,
            },
          };
        })),
      ...topicIds.map((topicId: any, index: any) => ({
        id: `edge-subject-${topicId}-${index}`,
        source: `subject-${data.subject}`,
        target: topicId,
        type: 'smoothStep',
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: EDGE_COLORS.SUBJECT_TO_CHAPTER.marker,
        },
        style: {
          strokeWidth: 2,
          stroke: EDGE_COLORS.SUBJECT_TO_CHAPTER.stroke,
        },
      }))
    ];

    return { nodes, edges };
  };

  const onNodesChange: OnNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds) as CustomNode[]),
    []
  );

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    []
  );

  const fitViewOptions: FitViewOptions = {
    padding: 0.2,
    duration: 200,
    includeHiddenNodes: false,
  };

  const onNodeClick: NodeMouseHandler = useCallback((event, node) => {
    const customNode = node as CustomNode;
    setSelectedNode(customNode);
  }, []);

  const closeModal = useCallback(() => {
    setSelectedNode(null);
  }, []);

  // Enhanced clear search function
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setDebouncedSearchQuery('');

    // Reset to initial viewport if available, otherwise use fitView
    if (initialViewportRef.current && hasInitialViewport) {
      setViewport(initialViewportRef.current, { duration: 500 });
    } else {
      setTimeout(() => {
        fitView({
          padding: 0.2,
          duration: 500,
          minZoom: 0.2,
          maxZoom: 2
        });
      }, 100);
    }
  }, [fitView, setViewport, hasInitialViewport]);

  return (
    <>
      <div style={{ height: '100%', width: '100%' }}>
        <ReactFlow
          colorMode={colorMode}
          nodes={filteredNodes}
          edges={edges}
          nodeTypes={nodeTypes}
          onNodeClick={onNodeClick}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          connectionLineType={ConnectionLineType.SmoothStep}
          fitViewOptions={fitViewOptions}
          defaultViewport={defaultViewport}
          minZoom={0.2}
          maxZoom={4}
          nodesConnectable={true}
          preventScrolling={true}
          fitView
          edgeTypes={edgeTypes}
          defaultEdgeOptions={{
            type: 'smoothStep',
            animated: true,
          }}
          className="h-full w-full"
        >
          <MiniMap
            zoomable
            pannable
            maskColor="hsl(var(--foreground) / 0.1)"
            nodeColor={(node) =>
              node.type === 'subject'
                ? 'hsl(var(--graph-subject))'
                : node.type === 'chapter'
                  ? 'hsl(var(--graph-chapter))'
                  : 'hsl(var(--graph-subtopic))'
            }
          />
          <Controls />
          <Panel position="top-right">
            <select
              onChange={(evt) => setColorMode(evt.target.value as ColorMode)}
              className="p-2 rounded border"
              value={colorMode}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System</option>
            </select>
          </Panel>

          <Panel position='top-center'>
            <div className="w-80 bg-white shadow-lg rounded-lg border border-gray-200">
              <div className="relative p-3">
                <input
                  type="text"
                  placeholder="Search topics, chapters, or descriptions..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full p-3 pl-4 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
                {searchQuery && (
                  <button
                    onClick={clearSearch}
                    className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Clear search"
                  >
                    <X size={18} />
                  </button>
                )}
                {debouncedSearchQuery && (
                  <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-blue-50 border border-blue-200 rounded-lg text-xs text-blue-700">
                    {filteredNodes.filter(node => {
                      const searchLower = debouncedSearchQuery.toLowerCase().trim();
                      const nameMatch = node.data.name.toLowerCase().includes(searchLower);
                      const descriptionMatch = (node.data.description ?? '').toLowerCase().includes(searchLower);
                      return nameMatch || descriptionMatch;
                    }).length} result(s) found
                  </div>
                )}
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>

      <Modal
        isOpen={!!selectedNode}
        onRequestClose={closeModal}
        ariaHideApp={false}
        contentLabel="Node Details"
        style={{
          overlay: {
            zIndex: 1000,
          },
          content: {
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: '400px',
            padding: '20px',
            borderRadius: '8px',
            boxShadow: '0 4px 6px hsl(var(--foreground) / 0.1)',
          },
        }}
      >
        {selectedNode && (
          <div>
            <h2 className="text-xl font-bold mb-4">{selectedNode.data.name}</h2>
            <p className="text-gray-700 mb-2">{selectedNode.data.description}</p>
            {selectedNode.type === 'subtopic' && (
              <>
                <p className="text-sm text-gray-600">
                  Proficiency: {Number(selectedNode.data.proficiency.toFixed(3))}%
                </p>
                <p className="text-sm text-gray-600">
                  Status: {selectedNode.data.status}
                </p>
              </>
            )}
            <button
              onClick={closeModal}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Close
            </button>
          </div>
        )}
      </Modal>
    </>
  );
};

// Wrapper component with ReactFlowProvider
const SubjectDetailsGraphWithProvider: React.FC<SubjectDetailsGraphProps> = ({ graphData }) => {
  return (
    <ReactFlowProvider>
      <SubjectDetailsGraph graphData={graphData} />
    </ReactFlowProvider>
  );
};

export default SubjectDetailsGraphWithProvider;