import React from "react";
import { useNavigate } from "react-router-dom";
import { X } from "lucide-react";
import { createPortal } from "react-dom";

interface RegisterTypeModalProps {
  open: boolean;
  onClose: () => void;
}

const RegisterTypeModal: React.FC<RegisterTypeModalProps> = ({ open, onClose }) => {
  const navigate = useNavigate();
  if (!open) return null;
  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="bg-white dark:bg-background rounded-xl shadow-xl px-8 py-8 relative w-[90vw] max-w-md">
        <button
          className="absolute top-4 right-4 text-foreground/60 hover:text-accent"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="w-6 h-6" />
        </button>
        <h2 className="text-2xl font-bold mb-4 text-center text-foreground">Choose Registration Type</h2>
        <div className="flex flex-col gap-4">
          <button
            className="bg-accent text-white px-6 py-3 rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-accent/20 hover:translate-y-[-2px] w-full"
            onClick={() => { navigate("/register?type=individual"); onClose(); }}
          >
            Register as Individual
          </button>
          <button
            className="bg-secondary text-accent px-6 py-3 rounded-lg font-medium transition-all hover:bg-accent/10 w-full border border-accent"
            onClick={() => { navigate("/register?type=institution"); onClose(); }}
          >
            Register Institutionally
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default RegisterTypeModal;
