import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import { Outlet } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import { usePageRefresh } from '../hooks/usePageRefresh';
import { useUser } from '@/contexts/userContext';
import 'react-toastify/dist/ReactToastify.css';


const Layout: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { user } = useUser();

  // Initialize authentication on layout mount
  usePageRefresh();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    handleResize(); // Initial check
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex flex-col sm:flex-row">
      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {/* Sidebar */}
      <Sidebar isExpanded={isExpanded} onToggle={() => setIsExpanded(!isExpanded)} />

      {/* Main Content - Adjust dynamically based on sidebar & screen size */}
      <main
        className={`grow z-40 lg:pt-0 lg:p-4 md:p-4 sm:p-2 h-screen transition-all duration-300 ease-in-out`}

        style={{
          marginLeft: isMobile ? '0' : isExpanded ? '16rem' : '4rem', // Only apply margin on larger screens
        }}
      >
        <Outlet />
      </main>

    </div>

  );
};

export default Layout;
