import React, { useRef, useState, useEffect } from 'react';
import { LinkIcon, PlusIcon, ArrowUpTrayIcon, XMarkIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { renderLatexContent } from './RenderLatexContent';
import TopicTagsInput from './TopicTagInput';
import SubtopicTagInput from './SubtopicTagInput';
import { axiosPrivate } from '../axios';
import ReactDOM from 'react-dom';
import { fetchWithCache } from '@/utils/cacheUtil';

interface Question {
    _id: string;
    answer: string;
    assignedDiff: string;
    difficulty: number;
    discrimination_parameter: number;
    images?: string[];
    options: string[];
    question: string;
    solution?: string;
    subtopic: string[];
    topic: string[];
}

interface QuestionCreatorProps {
    onClose: () => void;
    onAdd: (question: Omit<Question, '_id'>) => void;
    images: (path: string) => string;
    className: string;
    subject: string;
}

const QuestionCreator: React.FC<QuestionCreatorProps> = ({ onClose, onAdd, images, className, subject }) => {
    const prefix = 'https://aegisscholar-platform-web.s3.ap-south-1.amazonaws.com/uploads/images/questions/';
    const [question, setQuestion] = useState<Omit<Question, '_id'>>({
        question: '',
        answer: '',
        assignedDiff: '',
        difficulty: 1,
        discrimination_parameter: 0,
        images: [],
        options: ['', '', '', ''],
        solution: '',
        subtopic: [],
        topic: []
    });

    const [localImages, setLocalImages] = useState<File[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [topiclist, settopiclist] = useState<string[]>([]);
    const [subtopiclist, setsubtopiclist] = useState<string[]>([]);

    const fetchSuggestionsTopics = async (className: string, subject: string) => {
          try {
            if (!className || !subject) {
              return;
            }
            // const response = await axiosPrivate.get('/api/tslist/getTopics/', {classid: className, subject: subject});
            const response = await fetchWithCache(axiosPrivate, `/api/tslist/getTopics/${className}/${subject}`);
            settopiclist(response);
          } catch (error) {
            console.error(`[ERROR] fetching topics for the said subject: ${error}`);
          }
      }
    const fetchSuggestionsSubTopics = async (className: string, subject: string) => {
          try {
            if (!className || !subject) {
              return;
            }
            // const response = await axiosPrivate.get('/api/tslist/getTopics/', {classid: className, subject: subject});
            console.error(`[INFO] got classname: ${className} and subject: ${subject}`);
            const response = await fetchWithCache(axiosPrivate, `/api/tslist/getSubTopics/${className}/${subject}`);
            setsubtopiclist(response);
          } catch (error) {
            console.error(`[ERROR] fetching topics for the said subject: ${error}`);
          }
      }

    useEffect(() => {
      fetchSuggestionsTopics(className, subject);
      fetchSuggestionsSubTopics(className, subject);
    }, [])
    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        const newFiles = Array.from(files);
        setLocalImages(prev => [...prev, ...newFiles]);

        // Generate paths for the selected images and update the question object
        const newPaths = newFiles.map(file => `./images/${file.name.slice(0, -4)}.webp`);
        setQuestion(prev => ({
            ...prev,
            images: [...(prev.images || []), ...newPaths]
        }));
    };

    const handleRemoveImage = (index: number) => {
        setLocalImages(prev => prev.filter((_, idx) => idx !== index));
        setQuestion(prev => ({
            ...prev,
            images: prev.images?.filter((_, idx) => idx !== index)
        }));
    };

    const handleAddOption = () => {
        setQuestion({
            ...question,
            options: [...question.options, '']
        });
    };

    const handleRemoveOption = (indexToRemove: number) => {
        if (question.options.length > 2) {
            setQuestion({
                ...question,
                options: question.options.filter((_, index) => index !== indexToRemove)
            });
        }
    };

    const convertToWebP = async (file: File): Promise<File> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
                const img = new Image();
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    if (!ctx) return reject(new Error('Canvas context not available'));

                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(
                        (blob) => {
                            if (blob) {
                                const webpFile = new File([blob], file.name.replace(/\.\w+$/, '.webp'), {
                                    type: 'image/webp',
                                });
                                resolve(webpFile);
                            } else {
                                reject(new Error('Failed to convert image to WebP'));
                            }
                        },
                        'image/webp',
                        1.0 // Quality (0.0 to 1.0)
                    );
                };
                img.src = event.target?.result as string;
            };
            reader.onerror = (error) => reject(error);
            reader.readAsDataURL(file);
        });
    };

    const handleAdd = async () => {
        // Validation
        if (!question.question.trim()) {
            alert('Please add a question text');
            return;
        }
        if (question.options.some(opt => !opt.trim())) {
            alert('Please fill in all options');
            return;
        }
        if (!question.answer.trim()) {
            alert('Please specify the correct answer');
            return;
        }
        if (!question.topic || !question.subtopic) {
            alert('Please specify both topic and subtopic');
            return;
        }

        try {
            // Convert and upload images to S3
            if (localImages.length > 0) {
                const formData = new FormData();
                for (const file of localImages) {
                    const webpFile = await convertToWebP(file);
                    formData.append('images', webpFile);
                }

                const config = {
                    headers: {} // Let the interceptor handle it
                };

                const response = await axiosPrivate.post(
                    '/api/image/uploadMultipleImages',
                    formData,
                    config
                );

                if (response.data.images && response.data.images.length > 0) {
                    setQuestion(prev => ({
                        ...prev,
                        images: [
                            ...(prev.images || []),
                            ...response.data.images.map((img: any) => {
                                return img.url.replace(prefix, '');
                            })
                        ]
                    }));
                }
            }

            // Add the question
            onAdd(question);
            onClose();
        } catch (error: any) {
            console.error('Error uploading images:', error);
            alert('Failed to upload images. Please try again.');
        }
    };

    return ReactDOM.createPortal(
        <div className="fixed inset-0 backdrop-blur-sm bg-opacity-75 flex items-center justify-center z-60 p-2 sm:p-4">
            <div className="bg-card border border-border shadow-lg rounded-2xl w-full h-full md:h-auto md:max-h-[90vh] flex flex-col-reverse md:flex-row relative overflow-hidden">
                <button
                    onClick={onClose}
                    className="absolute top-2 right-2 sm:top-4 sm:right-4 text-muted-foreground hover:text-foreground transition-colors z-50"
                >
                    <XMarkIcon width={24} height={24} />
                </button>

                {/* Left Panel - Form */}
                <div className="flex-1 overflow-y-auto p-4 sm:p-6 h-full md:h-auto">
                    <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 text-primary pt-2">Create Question</h3>
                    <div className="space-y-4 sm:space-y-6">
                        {/* Basic Info Section */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                            <div className="col-span-1">
                                <label className="block text-sm font-medium mb-1 sm:mb-2 text-muted-foreground">Topic</label>
                                <TopicTagsInput
                                    value={question.topic}
                                    onChange={(value) => setQuestion({ ...question, topic: value })} disabled={false}
                topicList={topiclist}/>
                            </div>
                            <div className="col-span-1">
                                <label className="block text-sm font-medium mb-1 sm:mb-2 text-muted-foreground">Subtopics</label>
                                <SubtopicTagInput
                                    value={question.subtopic}
                                    onChange={(value) => setQuestion({ ...question, subtopic: value })}
                                    subtopiclist={subtopiclist}
                                />
                            </div>
                            <div className="col-span-1">
                                <label className="block text-sm font-medium mb-1 sm:mb-2 text-muted-foreground">Difficulty (1-10)</label>
                                <input
                                    type="number"
                                    min="1"
                                    max="10"
                                    className="w-full p-2 sm:p-3 border border-border rounded-xl focus:ring-1 focus:ring-accent focus:border-accent"
                                    value={question.difficulty}
                                    onChange={(e) => setQuestion({ ...question, difficulty: Number(e.target.value) })}
                                />
                            </div>
                        </div>

                        {/* Question Text */}
                        <div>
                            <label className="block text-sm font-medium mb-1 sm:mb-2 text-card-foreground">Question Text</label>
                            <textarea
                                className="w-full p-2 sm:p-3 border border-border rounded-xl focus:ring-1 focus:ring-accent focus:border-accent bg-background text-foreground"
                                rows={3}
                                value={question.question}
                                onChange={(e) => setQuestion({ ...question, question: e.target.value })}
                            />
                        </div>

                        {/* Images Section */}
                        <div>
                            <label className="block text-sm font-medium mb-1 sm:mb-2 text-muted-foreground">Images</label>
                            <div className="border-2 border-dashed border-border hover:cursor-pointer rounded-xl p-3 sm:p-4">
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    onChange={handleImageUpload}
                                    accept="image/*"
                                    multiple
                                    className="hidden"
                                />
                                <button
                                    onClick={() => fileInputRef.current?.click()}
                                    className="w-full py-2 flex items-center hover:cursor-pointer text-muted-foreground justify-center gap-2 text-sm sm:text-base"
                                >
                                    <ArrowUpTrayIcon width={20} height={20} />
                                    Upload Images (Limit: 5 Images at a time - Max 5MB each)
                                </button>
                            </div>
                            {localImages.length > 0 && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-3 sm:mt-4">
                                    {localImages.map((file, idx) => (
                                        <div key={idx} className="relative">
                                            <img
                                                src={URL.createObjectURL(file)}
                                                alt={`Preview ${idx + 1}`}
                                                className="w-full h-24 sm:h-32 object-cover rounded-lg"
                                            />
                                            <button
                                                onClick={() => handleRemoveImage(idx)}
                                                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full"
                                            >
                                                <XMarkIcon width={16} height={16} />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Options Section */}
                        <div>
                            <div className="flex items-center justify-between mb-1 sm:mb-2">
                                <label className="block text-sm font-medium text-card-foreground">Options</label>
                                <button
                                    onClick={handleAddOption}
                                    className="text-accent flex items-center gap-1 text-sm sm:text-base hover:text-accent-dark"
                                >
                                    <PlusIcon width={16} height={16} />
                                    Add Option
                                </button>
                            </div>
                            <div className="space-y-2 sm:space-y-3">
                                {question.options.map((option, idx) => (
                                    <div key={idx} className="flex items-center gap-2">
                                        <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center bg-accent-light text-accent rounded-full text-sm sm:text-base">
                                            {String.fromCharCode(65 + idx)}
                                        </span>
                                        <input
                                            type="text"
                                            className="flex-1 p-2 sm:p-3 border border-border rounded-xl text-sm sm:text-base bg-background text-foreground"
                                            value={option}
                                            onChange={(e) => {
                                                const newOptions = [...question.options];
                                                newOptions[idx] = e.target.value;
                                                setQuestion({ ...question, options: newOptions });
                                            }}
                                        />
                                        {question.options.length > 2 && (
                                            <button
                                                onClick={() => handleRemoveOption(idx)}
                                                className="text-muted-foreground hover:text-danger"
                                            >
                                                <XCircleIcon width={20} height={20} />
                                            </button>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Answer and Solution */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                            <div>
                                <label className="block text-sm font-medium mb-1 sm:mb-2 text-card-foreground">Correct Answer</label>
                                <input
                                    type="text"
                                    className="w-full p-2 sm:p-3 border border-border rounded-xl bg-background text-foreground"
                                    value={question.answer}
                                    onChange={(e) => setQuestion({ ...question, answer: e.target.value })}
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-1 sm:mb-2 text-card-foreground">Solution (Optional)</label>
                            <textarea
                                className="w-full p-2 sm:p-3 border border-border rounded-xl bg-background text-foreground"
                                rows={3}
                                value={question.solution || ''}
                                onChange={(e) => setQuestion({ ...question, solution: e.target.value })}
                            />
                        </div>

                    <div className="mt-4 sm:mt-6 flex justify-end sticky bottom-0 bg-card py-3 sm:py-4 border-t">
                        <button
                            onClick={handleAdd}
                            className="py-2 px-4 sm:px-6 bg-accent text-white rounded-lg hover:bg-accent/90 text-sm sm:text-base"
                            >
                            Add Question
                        </button>
                    </div>
                            </div>
                </div>

                {/* Right Panel - Preview */}
                <div className="flex-1 overflow-y-auto p-4 sm:p-6 bg-card border-b md:border-b-0 md:border-l border-border">
                    <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 text-primary">Preview</h3>
                    <div className="bg-background p-4 sm:p-6 rounded-xl shadow-lg border border-border">
                        <div className="mb-3 sm:mb-4 text-xs sm:text-sm text-muted-foreground">
                            {question.topic && question.subtopic && (
                                <p>{question.topic} › {question.subtopic}</p>
                            )}
                        </div>

                        {localImages.length > 0 && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-3 sm:mt-4">
                                    {localImages.map((file, idx) => (
                                        <div key={idx} className="relative">
                                            <img
                                                src={URL.createObjectURL(file)}
                                                alt={`Preview ${idx + 1}`}
                                                className="w-full h-24 sm:h-32 object-cover rounded-lg"
                                            />
                                            <button
                                                onClick={() => handleRemoveImage(idx)}
                                                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full"
                                            >
                                                <XMarkIcon width={16} height={16} />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        {/* {question.images && question.images
                            .filter(image => !image.includes('_a'))
                            .map((image, index) => (
                                <img
                                    key={index}
                                    src={prefix + image}
                                    className="w-full rounded-lg shadow-md mb-4 sm:mb-6"
                                    alt={`Question Image ${index + 1}`}
                                />
                            ))} */}

                        <p className="text-lg sm:text-xl mb-4 sm:mb-6 text-foreground">{renderLatexContent(question.question) || "Your question will appear here"}</p>

                        <div className="space-y-2 sm:space-y-3">
                            {question.options.map((option, idx) => (
                                <div
                                    key={idx}
                                    className={`p-2 sm:p-3 border rounded-xl ${question.answer === String.fromCharCode(65 + idx)
                                        ? 'bg-success-light border-success'
                                        : 'bg-muted border-border'
                                        }`}
                                >
                                    <span className="font-medium text-foreground">{String.fromCharCode(65 + idx)}.</span> <span className="text-foreground">{renderLatexContent(option) || `Option ${idx + 1}`}</span>
                                </div>
                            ))}
                        </div>

                        {question.solution && (
                            <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-accent-light rounded-xl">
                                <h4 className="font-medium mb-2 text-foreground">Solution:</h4>
                                <p className="text-sm sm:text-base text-foreground">{question.solution}</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default QuestionCreator;
