import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { useUser } from '@/contexts/userContext';
import { Button } from '@/shadcn/components/ui/button';
import { Input } from '@/shadcn/components/ui/input';
import { EnhancedMarkdown } from './RenderLatexContent';
import {
  PaperAirplaneIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  XMarkIcon,
  ChatBubbleOvalLeftIcon,
  EllipsisVerticalIcon,
  ArrowPathRoundedSquareIcon
} from '@heroicons/react/24/solid';
import { fetchWithCache } from '@/utils/cacheUtil';
import Orb from './HeroSectionBackground/Orb';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isLoading?: boolean;
}

interface ChatConversation {
  id: string;
  title: string;
  subject: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

interface AegisAiChatbotProps {
  subject: string;
  studentId: string;
  availableSubjects?: Array<{ subjectName: string }>;
  onSubjectChange?: (subject: string) => void;
}

const AegisAiChatbot: React.FC<AegisAiChatbotProps> = ({
  subject,
  studentId,
  availableSubjects = [],
  onSubjectChange
}) => {
  const axiosPrivate = useAxiosPrivate();
  const { user } = useUser();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [forceHideChatHistory, setForceHideChatHistory] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const [openConversationMenus, setOpenConversationMenus] = useState<Set<string>>(new Set());
  const [showSubjectSwitcher, setShowSubjectSwitcher] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const conversationMenuRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const subjectSwitcherRef = useRef<HTMLDivElement>(null);
  const chatHistoryButtonRef = useRef<HTMLButtonElement>(null);
  const chatHistoryCloseButtonRef = useRef<HTMLButtonElement>(null);
  const initializationRef = useRef(false);
  const creatingNewChatRef = useRef(false);

  // Generate storage keys
  const getConversationsStorageKey = () => `aegis_conversations_${studentId}_${subject}`;
  const getActiveConversationKey = () => `aegis_active_conversation_${studentId}_${subject}`;

  // Generate conversation title from first user message
  const generateConversationTitle = (messages: Message[]): string => {
    const firstUserMessage = messages.find(msg => msg.type === 'user');
    if (firstUserMessage) {
      const content = firstUserMessage.content.trim();
      if (content.length > 50) {
        return content.substring(0, 47) + '...';
      }
      return content;
    }
    return `${subject} Chat - ${new Date().toLocaleDateString()}`;
  };

  // Save conversations to database (with localStorage fallback)
  const saveConversationsToStorage = async (conversationsToSave: ChatConversation[]) => {
    try {
      // Save to localStorage as fallback
      const storageKey = getConversationsStorageKey();
      localStorage.setItem(storageKey, JSON.stringify(conversationsToSave));
    } catch (error) {
      console.error('Error saving conversations to localStorage:', error);
    }
  };

  // Load conversations from database (with localStorage fallback)
  const loadConversationsFromStorage = async (): Promise<ChatConversation[]> => {
    try {
      // Try to load from database first
      const response = await fetchWithCache(axiosPrivate, '/api/chat/conversations', {
        params: { userId: studentId, subject }
      });

      if (response.data && Array.isArray(response.data)) {
        return response.data.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }));
      }
    } catch (error) {
      console.error('Error loading conversations from database, falling back to localStorage:', error);

      // Fallback to localStorage
      try {
        const storageKey = getConversationsStorageKey();
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const parsedConversations = JSON.parse(stored);
          return parsedConversations.map((conv: any) => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt),
            messages: conv.messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }))
          }));
        }
      } catch (localError) {
        console.error('Error loading conversations from localStorage:', localError);
      }
    }
    return [];
  };

  // Save active conversation ID
  const saveActiveConversationId = (conversationId: string | null) => {
    try {
      const storageKey = getActiveConversationKey();
      if (conversationId) {
        localStorage.setItem(storageKey, conversationId);
      } else {
        localStorage.removeItem(storageKey);
      }
    } catch (error) {
      console.error('Error saving active conversation ID:', error);
    }
  };

  // Load active conversation ID
  const loadActiveConversationId = (): string | null => {
    try {
      const storageKey = getActiveConversationKey();
      return localStorage.getItem(storageKey);
    } catch (error) {
      console.error('Error loading active conversation ID:', error);
    }
    return null;
  };

  // Create a new conversation
  const createNewConversation = (): ChatConversation => {
    const newConversation: ChatConversation = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title: `New ${subject} Chat`,
      subject,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return newConversation;
  };

  // Update conversation with new messages
  const updateConversation = async (conversationId: string, newMessages: Message[]) => {
    try {
      // Update in database
      const title = newMessages.length > 1 ? generateConversationTitle(newMessages) : undefined;
      await axiosPrivate.put(`/api/chat/conversations/${conversationId}`, {
        userId: studentId,
        messages: newMessages,
        title
      });

      // Update local state
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            messages: newMessages,
            title: title || conv.title,
            updatedAt: new Date()
          };
        }
        return conv;
      });
      setConversations(updatedConversations);

      // Also save to localStorage as backup
      await saveConversationsToStorage(updatedConversations);
    } catch (error) {
      console.error('Error updating conversation in database:', error);

      // Fallback to localStorage only
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            messages: newMessages,
            title: newMessages.length > 1 ? generateConversationTitle(newMessages) : conv.title,
            updatedAt: new Date()
          };
        }
        return conv;
      });
      setConversations(updatedConversations);
      await saveConversationsToStorage(updatedConversations);
    }
  };

  // Start a new chat
  const startNewChat = async (existingConversations?: ChatConversation[]) => {
    const currentConversations = existingConversations || conversations;
    console.log(`[START NEW CHAT] Called - current conversations count: ${currentConversations.length}`);
    console.log(`[START NEW CHAT] Current conversations:`, currentConversations.map(c => c.id));

    // Prevent multiple simultaneous chat creations
    if (creatingNewChatRef.current) {
      console.log(`[START NEW CHAT] Already creating a new chat, skipping...`);
      return;
    }

    creatingNewChatRef.current = true;

    try {
      const newConversation = createNewConversation();
      // No welcome message - start with empty conversation
      newConversation.messages = [];
      console.log(`[START NEW CHAT] Created new conversation: ${newConversation.id}`);

      // Create conversation in database
      await axiosPrivate.post('/api/chat/conversations', {
        id: newConversation.id,
        userId: studentId,
        title: newConversation.title,
        subject: newConversation.subject,
        messages: newConversation.messages
      });
      console.log(`[START NEW CHAT] Saved to database successfully`);

      // Update local state
      const updatedConversations = [newConversation, ...currentConversations];
      console.log(`[START NEW CHAT] Updated conversations count: ${updatedConversations.length}`);
      setConversations(updatedConversations);
      setActiveConversationId(newConversation.id);
      setMessages([]);

      // Save to localStorage as backup
      await saveConversationsToStorage(updatedConversations);
      saveActiveConversationId(newConversation.id);
      console.log(`[START NEW CHAT] Completed successfully`);
    } catch (error) {
      console.error('[START NEW CHAT] Error creating new conversation in database:', error);

      // Fallback to localStorage only
      const newConversation = createNewConversation();
      // No welcome message - start with empty conversation
      newConversation.messages = [];
      const updatedConversations = [newConversation, ...currentConversations];
      console.log(`[START NEW CHAT] Fallback - Updated conversations count: ${updatedConversations.length}`);
      setConversations(updatedConversations);
      setActiveConversationId(newConversation.id);
      setMessages([]);
      await saveConversationsToStorage(updatedConversations);
      saveActiveConversationId(newConversation.id);
      console.log(`[START NEW CHAT] Fallback completed`);
    } finally {
      // Reset the flag
      creatingNewChatRef.current = false;
      console.log(`[START NEW CHAT] Flag reset`);
    }
  };

  // Load a specific conversation
  const loadConversation = (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation) {
      setActiveConversationId(conversationId);
      setMessages(conversation.messages);
      saveActiveConversationId(conversationId);
    }
  };

  // Delete a conversation
  const deleteConversation = async (conversationId: string) => {
    console.log(`[DELETE] Starting deletion of conversation: ${conversationId}`);
    console.log(`[DELETE] Current conversations count: ${conversations.length}`);

    try {
      // Delete from database
      console.log(`[DELETE] Sending delete request to backend for conversation: ${conversationId}`);
      const response = await axiosPrivate.delete(`/api/chat/conversations/${conversationId}`, {
        params: { userId: studentId }
      });
      console.log(`[DELETE] Backend deletion successful:`, response.data);

      // Update local state
      const updatedConversations = conversations.filter(conv => conv.id !== conversationId);
      console.log(`[DELETE] Updated conversations count: ${updatedConversations.length}`);
      setConversations(updatedConversations);
      await saveConversationsToStorage(updatedConversations);

      if (activeConversationId === conversationId) {
        if (updatedConversations.length > 0) {
          console.log(`[DELETE] Loading next conversation: ${updatedConversations[0].id}`);
          loadConversation(updatedConversations[0].id);
        } else {
          console.log(`[DELETE] No conversations left, starting new chat`);
          await startNewChat(updatedConversations);
        }
      }

      console.log(`[DELETE] Conversation deletion completed successfully`);
    } catch (error) {
      console.error(`[DELETE] Error deleting conversation from database:`, error);
      console.log(`[DELETE] Falling back to localStorage-only deletion`);

      // Fallback to localStorage only
      const updatedConversations = conversations.filter(conv => conv.id !== conversationId);
      setConversations(updatedConversations);
      await saveConversationsToStorage(updatedConversations);

      if (activeConversationId === conversationId) {
        if (updatedConversations.length > 0) {
          loadConversation(updatedConversations[0].id);
        } else {
          await startNewChat(updatedConversations);
        }
      }
    }
  };

  // Export specific conversation history as JSON
  const exportConversationHistory = (conversation: ChatConversation) => {
    try {
      const chatData = {
        subject,
        studentId,
        conversationId: conversation.id,
        conversationTitle: conversation.title,
        exportDate: new Date().toISOString(),
        messages: conversation.messages.filter(msg => !msg.isLoading) // Exclude loading messages
      };

      const dataStr = JSON.stringify(chatData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `aegis-chat-${conversation.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting conversation history:', error);
    }
  };

  // Toggle conversation menu
  const toggleConversationMenu = (conversationId: string) => {
    setOpenConversationMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(conversationId)) {
        newSet.delete(conversationId);
      } else {
        // Close all other menus and open this one
        newSet.clear();
        newSet.add(conversationId);
      }
      return newSet;
    });
  };

  // Close conversation menu
  const closeConversationMenu = (conversationId: string) => {
    setOpenConversationMenus(prev => {
      const newSet = new Set(prev);
      newSet.delete(conversationId);
      return newSet;
    });
  };

  // Toggle subject switcher dropdown
  const toggleSubjectSwitcher = () => {
    setShowSubjectSwitcher(prev => !prev);
  };

  // Close subject switcher dropdown
  const closeSubjectSwitcher = () => {
    setShowSubjectSwitcher(false);
  };

  // Optimized chat history toggle handler with accessibility and focus management
  const handleChatHistoryToggle = useCallback((e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
    setShowChatHistory(prev => {
      const newState = !prev;

      // Focus management for accessibility
      if (newState) {
        // When opening, focus the close button after a short delay
        setTimeout(() => {
          chatHistoryCloseButtonRef.current?.focus();
        }, 100);
      } else {
        // When closing, focus the chat history button after a short delay
        setTimeout(() => {
          chatHistoryButtonRef.current?.focus();
        }, 100);
      }

      return newState;
    });
  }, []);

  // Handle keyboard navigation for chat history button
  const handleChatHistoryKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleChatHistoryToggle(e);
    }
  }, [handleChatHistoryToggle]);

  // Memoized conversations count for performance
  const conversationsCount = useMemo(() => conversations.length, [conversations.length]);



  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      const wasMobile = isMobile;
      setIsMobile(mobile);

      // Auto-close chat history when switching from desktop to mobile
      if (!wasMobile && mobile && showChatHistory) {
        setShowChatHistory(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isMobile, showChatHistory]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Debug: Track showChatHistory state changes
  useEffect(() => {
    console.log('[CHAT HISTORY] State changed to:', showChatHistory);
  }, [showChatHistory]);

  // Handle mobile state changes - ensure chat history is properly managed
  useEffect(() => {
    // When switching to mobile, ensure chat history behavior is correct
    if (isMobile && showChatHistory) {
      // Add a small delay to ensure the mobile layout has been applied
      const timer = setTimeout(() => {
        // Force a re-render to ensure proper mobile modal behavior
        setShowChatHistory(prev => prev);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isMobile, showChatHistory]);

  // Handle escape key to close chat history (especially useful in inspect element mode)
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showChatHistory) {
        console.log('[CHAT HISTORY] Escape key pressed, closing chat history');
        setShowChatHistory(false);
      }
    };

    if (showChatHistory) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showChatHistory]);



  // Close conversation menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      let clickedInsideAnyMenu = false;

      conversationMenuRefs.current.forEach((ref) => {
        if (ref && ref.contains(event.target as Node)) {
          clickedInsideAnyMenu = true;
        }
      });

      if (!clickedInsideAnyMenu && openConversationMenus.size > 0) {
        setOpenConversationMenus(new Set());
      }
    };

    if (openConversationMenus.size > 0) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openConversationMenus]);

  // Close subject switcher when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (subjectSwitcherRef.current && !subjectSwitcherRef.current.contains(event.target as Node)) {
        setShowSubjectSwitcher(false);
      }
    };

    if (showSubjectSwitcher) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSubjectSwitcher]);



  // Load conversations when component mounts or subject changes
  useEffect(() => {
    const initializeChatSession = async () => {
      // Prevent double execution in React Strict Mode
      if (initializationRef.current) {
        console.log(`[CHAT INIT] Skipping duplicate initialization call`);
        return;
      }
      initializationRef.current = true;

      try {
        console.log(`[CHAT INIT] Initializing chat for subject: ${subject}, user: ${user?.username}`);

        // Check if this is a genuine login event
        const loginSessionKey = `aegis_login_session_${studentId}`;
        const lastLoginSession = localStorage.getItem(loginSessionKey);
        const currentSessionId = sessionStorage.getItem('sessionId'); // Set during login

        const isGenuineLogin = currentSessionId && currentSessionId !== lastLoginSession;

        console.log(`[CHAT INIT] Login check - Current session: ${currentSessionId}, Last session: ${lastLoginSession}, Is genuine login: ${isGenuineLogin}`);

        if (isGenuineLogin) {
          // This is a genuine login - clean up duplicates first, then create new conversation
          console.log(`[CHAT INIT] Genuine login detected, cleaning up duplicates and creating new conversation`);

          // Clean up any duplicate empty conversations first
          try {
            await axiosPrivate.post('/api/chat/cleanup-duplicates', {
              userId: studentId,
              subject
            });
            console.log(`[CHAT INIT] Duplicate cleanup completed`);
          } catch (cleanupError) {
            console.warn('Error during duplicate cleanup:', cleanupError);
            // Continue even if cleanup fails
          }

          await createNewConversationForLogin(true);
          localStorage.setItem(loginSessionKey, currentSessionId);
        } else {
          // This is navigation/refresh - load existing conversations
          console.log(`[CHAT INIT] Navigation/refresh detected, loading existing conversations`);

          // First, try to load existing conversations from localStorage
          const storedConversations = await loadConversationsFromStorage();
          const activeId = loadActiveConversationId();

          console.log(`[CHAT INIT] Found ${storedConversations.length} stored conversations`);

          if (storedConversations.length > 0) {
            // Load existing conversations
            setConversations(storedConversations);

            // Try to restore the active conversation
            const conversationToLoad = activeId
              ? storedConversations.find(conv => conv.id === activeId) || storedConversations[0]
              : storedConversations[0];

            setActiveConversationId(conversationToLoad.id);
            setMessages(conversationToLoad.messages);
            saveActiveConversationId(conversationToLoad.id);

            console.log(`[CHAT INIT] Restored conversation: ${conversationToLoad.id} with ${conversationToLoad.messages.length} messages`);
          } else {
            // No local conversations - try to load from database
            console.log(`[CHAT INIT] No local conversations, loading from database`);
            await loadConversationsFromDatabase();
          }
        }
      } catch (error) {
        console.error('Error initializing chat session:', error);
        // Fallback to loading from database or creating new conversation
        await loadConversationsFromDatabase();
      } finally {
        // Reset the flag after a short delay to allow for dependency changes
        setTimeout(() => {
          initializationRef.current = false;
        }, 1000);
      }
    };

    initializeChatSession();
  }, [subject, user?.username, studentId]);

  // Create new conversation for genuine login
  const createNewConversationForLogin = async (isNewLogin = false) => {
    try {
      const response = await axiosPrivate.post('/api/chat/create-on-login', {
        userId: studentId,
        subject,
        isNewLogin
      });

      if (response.data?.conversation) {
        const newConversation = {
          ...response.data.conversation,
          createdAt: new Date(response.data.conversation.createdAt),
          updatedAt: new Date(response.data.conversation.updatedAt),
          messages: response.data.conversation.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        };

        // For genuine login, load all existing conversations first, then handle the returned conversation
        if (isNewLogin) {
          try {
            // Load all existing conversations from database
            const existingConversations = await loadConversationsFromStorage();

            // Check if the returned conversation is a reused one (already exists) or a new one
            const isReusedConversation = response.data.isReused ||
              existingConversations.some(conv => conv.id === newConversation.id);

            let allConversations: ChatConversation[];

            if (isReusedConversation) {
              // If it's a reused conversation, update it in the existing list and move to front
              allConversations = existingConversations.filter(conv => conv.id !== newConversation.id);
              allConversations.unshift(newConversation);
              console.log(`[CHAT INIT] Reused existing conversation: ${newConversation.id}`);
            } else {
              // If it's a new conversation, add it to the beginning
              allConversations = [newConversation, ...existingConversations];
              console.log(`[CHAT INIT] New conversation created and added to ${existingConversations.length} existing conversations`);
            }

            setConversations(allConversations);
            setActiveConversationId(newConversation.id);
            setMessages(newConversation.messages);

            // Save complete list to localStorage
            await saveConversationsToStorage(allConversations);
            saveActiveConversationId(newConversation.id);
          } catch (error) {
            console.error('Error loading existing conversations during login:', error);
            // Fallback to just the new conversation
            setConversations([newConversation]);
            setActiveConversationId(newConversation.id);
            setMessages(newConversation.messages);
            await saveConversationsToStorage([newConversation]);
            saveActiveConversationId(newConversation.id);
          }
        } else {
          // For navigation/refresh, just set the returned conversation
          setConversations([newConversation]);
          setActiveConversationId(newConversation.id);
          setMessages(newConversation.messages);
          await saveConversationsToStorage([newConversation]);
          saveActiveConversationId(newConversation.id);
        }

        console.log(`[CHAT INIT] Conversation ready: ${newConversation.id}`);
      } else {
        // Fallback to manual creation
        await startNewChat();
      }
    } catch (error) {
      console.error('Error creating new conversation for session:', error);
      await startNewChat();
    }
  };

  // Load conversations from database when localStorage is empty
  const loadConversationsFromDatabase = async () => {
    try {
      const response = await fetchWithCache(axiosPrivate, '/api/chat/conversations', {
        params: { userId: studentId, subject }
      });

      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        const conversations = response.data.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }));

        setConversations(conversations);
        const mostRecent = conversations[0];
        setActiveConversationId(mostRecent.id);
        setMessages(mostRecent.messages);

        // Save to localStorage for future use
        await saveConversationsToStorage(conversations);
        saveActiveConversationId(mostRecent.id);

        console.log(`[CHAT INIT] Loaded ${conversations.length} conversations from database`);
      } else {
        // No conversations in database either - create new one
        await createNewConversationForLogin(false);
      }
    } catch (error) {
      console.error('Error loading conversations from database:', error);
      await createNewConversationForLogin(false);
    }
  };

  // Update conversation when messages change (but filter out loading messages)
  useEffect(() => {
    if (messages.length > 0 && activeConversationId) {
      // Filter out loading messages before saving to prevent validation errors
      const validMessages = messages.filter(msg => !msg.isLoading && msg.content.trim() !== '');

      if (validMessages.length > 0) {
        // Don't await here to avoid blocking the UI
        updateConversation(activeConversationId, validMessages).catch(error => {
          console.error('Error updating conversation:', error);
        });
      }
    }
  }, [messages, activeConversationId]);

  // Check if current conversation is empty (no messages)
  const isCurrentConversationEmpty = () => {
    return messages.length === 0 || messages.every(msg => msg.isLoading);
  };

  // Check if we can delete a conversation (prevent deleting if it would leave only empty chats)
  const canDeleteConversation = (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (!conversation) return false;

    // If this conversation has messages, it can always be deleted
    if (conversation.messages.length > 0) return true;

    // If this is an empty conversation, check if there are other non-empty conversations
    const nonEmptyConversations = conversations.filter(conv =>
      conv.id !== conversationId && conv.messages.length > 0
    );

    // Only allow deletion if there are other non-empty conversations, or if there are multiple empty ones
    const emptyConversations = conversations.filter(conv => conv.messages.length === 0);
    return nonEmptyConversations.length > 0 || emptyConversations.length > 1;
  };

  // Function to explicitly create a new conversation (user-initiated)
  const createNewConversationExplicit = async () => {
    // Check if current conversation is already empty
    if (isCurrentConversationEmpty()) {
      console.log(`[CHAT] Current conversation is already empty, not creating new one`);
      return;
    }

    try {
      console.log(`[CHAT] User requested new conversation`);

      const response = await axiosPrivate.post('/api/chat/create-new', {
        userId: studentId,
        subject
      });

      if (response.data?.conversation) {
        const newConversation = {
          ...response.data.conversation,
          createdAt: new Date(response.data.conversation.createdAt),
          updatedAt: new Date(response.data.conversation.updatedAt),
          messages: response.data.conversation.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        };

        // Add to conversations list and set as active
        const updatedConversations = [newConversation, ...conversations];
        setConversations(updatedConversations);
        setActiveConversationId(newConversation.id);
        setMessages(newConversation.messages);

        // Save to localStorage
        await saveConversationsToStorage(updatedConversations);
        saveActiveConversationId(newConversation.id);

        console.log(`[CHAT] New conversation created: ${newConversation.id}`);
      }
    } catch (error) {
      console.error('Error creating new conversation:', error);
      // Fallback to startNewChat only if current conversation is not empty
      if (!isCurrentConversationEmpty()) {
        await startNewChat();
      }
    }
  };

  // Detect if this is a teacher request
  const isTeacherMode = studentId.startsWith('teacher_');

  const getSuggestedQuestions = () => {
    const subjectLower = subject.toLowerCase();

    if (isTeacherMode) {
      // Teacher-specific prompts
      if (subjectLower.includes('math')) {
        return [
          "How is my class performing in mathematics?",
          "Which students are struggling with algebra?",
          "Show me recent test trends for this class",
          "Identify students who need additional support"
        ];
      } else if (subjectLower.includes('physics')) {
        return [
          "How is my physics class progressing?",
          "Which students need help with mechanics?",
          "Show me class performance on recent tests",
          "Identify struggling students in physics"
        ];
      } else if (subjectLower.includes('chemistry')) {
        return [
          "How is my chemistry class performing?",
          "Which students struggle with chemical equations?",
          "Show me class trends in organic chemistry",
          "Identify students needing extra support"
        ];
      } else if (subjectLower.includes('biology')) {
        return [
          "How is my biology class doing overall?",
          "Which students need help with genetics?",
          "Show me recent class performance trends",
          "Identify students requiring intervention"
        ];
      } else {
        return [
          `How is my ${subject} class performing?`,
          `Which students need additional support?`,
          `Show me recent test trends for this class`,
          `Identify struggling students in ${subject}`
        ];
      }
    } else {
      // Student-specific prompts (existing)
      if (subjectLower.includes('math')) {
        return [
          "What are my weakest areas in mathematics?",
          "How can I improve my problem-solving skills?",
          "What should I study next in math?",
          "Explain quadratic equations in simple terms"
        ];
      } else if (subjectLower.includes('physics')) {
        return [
          "What physics concepts am I struggling with?",
          "How do I approach physics word problems?",
          "What are Newton's laws in simple terms?",
          "How can I visualize physics concepts better?"
        ];
      } else if (subjectLower.includes('chemistry')) {
        return [
          "What chemistry topics need more practice?",
          "How do I balance chemical equations?",
          "Explain chemical bonding simply",
          "What's my progress in organic chemistry?"
        ];
      } else if (subjectLower.includes('biology')) {
        return [
          "What biology concepts am I weak in?",
          "How does photosynthesis work?",
          "Explain genetics in simple terms",
          "What should I focus on in biology?"
        ];
      } else {
        return [
          `What are my knowledge gaps in ${subject}?`,
          `How can I improve in ${subject}?`,
          `What should I study next?`,
          `Give me a study strategy for ${subject}`
        ];
      }
    }
  };

  // Helper function to send message with specific content (for suggested questions)
  const handleSendMessageWithContent = async (messageContent: string) => {
    if (!messageContent.trim() || isLoading) return;

    const startTime = Date.now();

    try {
      // Prepare conversation history for context (optimized - reduced from 6 to 4)
      const conversationHistory = messages
        .filter(msg => !msg.isLoading)
        .slice(-4) // Last 4 messages for better performance
        .map(msg => ({
          role: msg.type, // Map 'type' to 'role' for text-to-query service compatibility
          content: msg.content
        }));

      // Call the enhanced Text-to-MongoDB Query System API endpoint for optimized data retrieval
      const response = await axiosPrivate.post('/api/teaching-assistant/chat-query', {
        studentId,
        subject,
        message: messageContent,
        conversationHistory,
        conversationId: activeConversationId,
        userName: user?.firstName || user?.username || 'there'
      });

      const responseTime = Date.now() - startTime;
      console.log(`[CHAT RESPONSE] Received in ${responseTime}ms`);

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: response.data.message || response.data.content || "I understand you're asking about this topic. Let me help you explore it further!",
        timestamp: new Date()
      };

      // Remove loading message and add actual response
      setMessages(prev => prev.filter(msg => !msg.isLoading).concat(assistantMessage));
    } catch (error: any) {
      console.error('Error getting AI response:', error);

      // Enhanced error handling with specific messages
      let errorContent = "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.";

      if (error?.response?.status === 429) {
        errorContent = "I'm receiving a lot of requests right now. Please wait a moment and try again.";
      } else if (error?.response?.status >= 500) {
        errorContent = "There's a temporary issue with my systems. Please try again shortly.";
      } else if (!navigator.onLine) {
        errorContent = "It looks like you're offline. Please check your connection and try again.";
      }

      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'assistant',
        content: errorContent,
        timestamp: new Date()
      };

      setMessages(prev => prev.filter(msg => !msg.isLoading).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    const loadingMessage: Message = {
      id: `loading-${Date.now()}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true
    };

    // Optimistic UI update
    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputValue('');
    setIsLoading(true);

    // Use the helper function to send the message
    await handleSendMessageWithContent(userMessage.content);
  };



  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };



  return (
    <div className="flex h-full bg-background relative">
      {/* Mobile Overlay */}
      {isMobile && showChatHistory === true && !forceHideChatHistory && (
        <div
          className="fixed inset-0 bg-black/50 dark:bg-black/70 z-40 touch-manipulation"
          onClick={(e) => {
            e.stopPropagation();
            console.log('[CHAT HISTORY] Overlay clicked, closing chat history');
            setShowChatHistory(false);
          }}
          role="button"
          aria-label="Close chat history"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setShowChatHistory(false);
            }
          }}
        />
      )}

      {/* Chat History Sidebar */}
      <div
        id="chat-history-sidebar"
        className={`${isMobile
          ? `fixed left-0 top-0 h-full w-80 z-50 transform transition-transform duration-300 ${showChatHistory ? 'translate-x-0' : '-translate-x-full'
          } ${!showChatHistory ? 'pointer-events-none' : 'pointer-events-auto'}`
          : `${showChatHistory ? 'w-80' : 'w-0'} transition-all duration-300 ${!showChatHistory ? 'overflow-hidden' : ''}`
          } bg-background dark:bg-background shadow-lg dark:shadow-2xl`}
        style={isMobile && (!showChatHistory || forceHideChatHistory) ? { display: 'none' } : {}}
        role="complementary"
        aria-label="Chat history sidebar"
        aria-hidden={!showChatHistory}>
        {/* Always render content on mobile, conditionally on desktop */}
        {(isMobile || showChatHistory) && !forceHideChatHistory && (
          <div className="h-full flex flex-col">
            {/* Sidebar Header */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center justify-between">
                <h3
                  className="font-semibold text-foreground dark:text-foreground cursor-pointer"
                  onDoubleClick={() => {
                    console.log('[CHAT HISTORY] Header double-clicked, force closing');
                    setShowChatHistory(false);
                  }}
                  title="Double-click to close"
                >
                  Chat History
                </h3>
                <Button
                  ref={chatHistoryCloseButtonRef}
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('[CHAT HISTORY] Close button clicked, current state:', showChatHistory, 'isMobile:', isMobile);

                    // Force hide immediately
                    setForceHideChatHistory(true);
                    setShowChatHistory(false);

                    console.log('[CHAT HISTORY] State set to false and force hide enabled');

                    // Reset force hide after animation
                    setTimeout(() => {
                      setForceHideChatHistory(false);
                      console.log('[CHAT HISTORY] Force hide reset');
                    }, 500);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      e.currentTarget.click();
                    }
                  }}
                  className="p-1 h-8 w-8 touch-manipulation hover:bg-muted transition-all duration-200"
                  type="button"
                  aria-label="Close chat history sidebar"
                  title="Close chat history"
                >
                  <XMarkIcon className="text-primary h-4 w-4" aria-hidden="true" />
                </Button>
              </div>
              <Button
                onClick={() => {
                  createNewConversationExplicit().catch(error => {
                    console.error('Error starting new chat:', error);
                  });
                }}
                className="w-full mt-3 flex items-center gap-2"
                size="sm"
                disabled={isCurrentConversationEmpty()}
                title={isCurrentConversationEmpty() ? "Current conversation is already empty" : "Start a new conversation"}
              >
                <PlusIcon className="h-4 w-4" />
                New Chat
              </Button>
            </div>

            {/* Conversations List */}
            <div className={`flex-1 overflow-y-auto p-2 ${isMobile ? 'pb-20' : ''}`}>
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`p-3 mb-2 rounded-lg transition-colors group touch-manipulation relative ${activeConversationId === conversation.id
                    ? 'bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30'
                    : 'bg-background dark:bg-card hover:bg-muted/50 dark:hover:bg-muted/30 border border-transparent dark:border-transparent'
                    }`}
                >
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      loadConversation(conversation.id);
                      // Close sidebar on mobile after selecting conversation
                      if (isMobile) {
                        setShowChatHistory(false);
                      }
                    }}
                    className="flex items-start justify-between cursor-pointer"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm text-foreground dark:text-foreground truncate">
                        {conversation.title}
                      </p>
                      <p className="text-xs text-muted-foreground dark:text-muted-foreground mt-1">
                        {conversation.updatedAt.toLocaleDateString()} • {conversation.messages.length} messages
                      </p>
                    </div>

                    {/* 3-dots menu button */}
                    <div className="relative" ref={(el) => {
                      if (el) conversationMenuRefs.current.set(conversation.id, el);
                    }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleConversationMenu(conversation.id);
                        }}
                        className="p-1 h-6 w-6 opacity-100 hover:bg-muted dark:hover:bg-muted/50"
                      >
                        <EllipsisVerticalIcon className="h-5 w-5 text-muted-foreground dark:text-muted-foreground" />
                      </Button>

                      {/* Dropdown menu */}
                      {openConversationMenus.has(conversation.id) && (
                        <div className="absolute right-0 top-full mt-1 bg-background dark:bg-popover border border-border dark:border-border rounded-lg shadow-lg dark:shadow-2xl z-50 min-w-[160px]">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              exportConversationHistory(conversation);
                              closeConversationMenu(conversation.id);
                            }}
                            className="w-full text-left px-3 py-2 hover:bg-muted dark:hover:bg-muted/50 transition-colors first:rounded-t-lg flex items-center gap-2 text-sm text-foreground dark:text-foreground"
                          >
                            <ArrowDownTrayIcon className="h-4 w-4" />
                            Export Chat
                          </button>
                          <button
                            onClick={async (e) => {
                              e.stopPropagation();
                              const conversationIdToDelete = conversation.id; // Capture the ID
                              console.log(`[UI DELETE] Delete button clicked for conversation: ${conversationIdToDelete}`);

                              // Check if deletion is allowed
                              if (!canDeleteConversation(conversationIdToDelete)) {
                                console.log(`[UI DELETE] Deletion not allowed - would leave only empty conversations`);
                                alert('Cannot delete this conversation. You must have at least one conversation.');
                                closeConversationMenu(conversationIdToDelete);
                                return;
                              }

                              // TEMPORARY: Skip confirmation for testing
                              console.log(`[UI DELETE] Skipping confirmation dialog for testing...`);
                              const userConfirmed = true; // Always proceed
                              console.log(`[UI DELETE] Confirmation result:`, userConfirmed);

                              if (userConfirmed) {
                                console.log(`[UI DELETE] User confirmed deletion`);
                                try {
                                  console.log(`[UI DELETE] Calling deleteConversation function`);
                                  await deleteConversation(conversationIdToDelete);
                                  console.log(`[UI DELETE] Delete function completed successfully`);
                                } catch (error) {
                                  console.error('[UI DELETE] Error deleting conversation:', error);
                                  alert('Failed to delete conversation. Please try again.');
                                }
                              } else {
                                console.log(`[UI DELETE] User cancelled deletion or dialog failed`);
                              }
                              // Always close menu after user makes a choice
                              console.log(`[UI DELETE] Closing conversation menu`);
                              closeConversationMenu(conversationIdToDelete);
                            }}
                            disabled={!canDeleteConversation(conversation.id)}
                            className={`w-full text-left px-3 py-2 transition-colors last:rounded-b-lg flex items-center gap-2 text-sm ${canDeleteConversation(conversation.id)
                              ? 'hover:bg-muted dark:hover:bg-muted/50 text-destructive dark:text-destructive hover:text-destructive dark:hover:text-destructive cursor-pointer'
                              : 'text-muted-foreground dark:text-muted-foreground cursor-not-allowed opacity-50'
                              }`}
                            title={!canDeleteConversation(conversation.id) ? "Cannot delete - you must have at least one conversation" : "Delete this conversation"}
                          >
                            <TrashIcon className="h-4 w-4" />
                            Delete Chat
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {conversations.length === 0 && (
                <div className={`text-center py-8 text-muted-foreground dark:text-muted-foreground ${isMobile ? 'pb-20' : ''}`}>
                  <ChatBubbleOvalLeftIcon className="h-12 w-12 text-primary dark:text-primary mx-auto mb-2 opacity-50" />
                  <p className="text-sm text-muted-foreground dark:text-muted-foreground">No conversations yet</p>
                  <p className="text-xs text-muted-foreground dark:text-muted-foreground">Start a new chat to begin</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header with Chat History */}
        <div className="border-b border-border p-3 md:p-4 bg-muted/20 dark:bg-muted/10">
          <div className="flex items-center justify-between">
            {/* Chat History Button - Hidden when chat history is open */}
            {!showChatHistory && (
              <Button
                ref={chatHistoryButtonRef}
                variant="outline"
                size="sm"
                onClick={handleChatHistoryToggle}
                onKeyDown={handleChatHistoryKeyDown}
                className="relative p-2 h-8 w-8 md:h-9 md:w-9 touch-manipulation group transition-all duration-200 cursor-pointer hover:bg-primary/10 hover:shadow-sm dark:hover:bg-accent dark:hover:text-accent-foreground focus:outline-none"
                type="button"
                aria-label={showChatHistory ?
                  `Close chat history sidebar. ${conversationsCount} conversation${conversationsCount !== 1 ? 's' : ''} available.` :
                  `Open chat history sidebar. ${conversationsCount} conversation${conversationsCount !== 1 ? 's' : ''} available.`
                }
                aria-expanded={showChatHistory}
                aria-controls="chat-history-sidebar"
                aria-haspopup="dialog"
                role="button"
                tabIndex={0}
              >
                <ChatBubbleOvalLeftIcon
                  className="text-primary group-hover:text-primary h-4 w-4 transition-colors duration-200 pointer-events-none"
                  aria-hidden="true"
                />
                <span className="sr-only">
                  {showChatHistory ? 'Close' : 'Open'} chat history.
                  {conversationsCount} conversation{conversationsCount !== 1 ? 's' : ''} available.
                </span>
              </Button>
            )}

            {/* Spacer when chat history button is hidden */}
            {showChatHistory && (
              <div className="w-8 md:w-9" aria-hidden="true" />
            )}
            <div className="flex-shrink-0 bg-background dark:bg-background">
              <div className="flex items-center text-center gap-2">
                <div className="flex-shrink-0 w-7 h-7 md:w-8 md:h-8">
                  <Orb
                    hoverIntensity={0.8}
                    rotateOnHover={true}
                    hue={0}
                    forceHoverState={false}
                  />
                </div>
                <h1 className="text-xl font-bold font-['Space_Grotesk'] text-primary dark:text-primary">AegisAI</h1>
              </div>
            </div>
            <div className="w-8 md:w-9">
              {/* Spacer for symmetry */}
            </div>
          </div>
        </div>

        {/* Chat Messages Area */}
        <div className={`flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 ${messages.length === 0 ? 'flex items-center justify-center' : ''}`}>
          {messages.length === 0 ? (
            /* Welcome Content - Centered */
            <div className="text-center max-w-2xl mx-auto px-4">
              {/* Welcome Message */}
              <div className="mb-8">
                {/* <div className="w-16 h-16 mx-auto mb-4">
                  <Orb
                    hoverIntensity={0.3}
                    rotateOnHover={true}
                    hue={0}
                    forceHoverState={false}
                  />
                </div> */}
                <h2 className="text-2xl font-bold text-foreground dark:text-foreground mb-3">
                  Hi {user?.firstName || 'there'}!
                </h2>
                <p className="text-muted-foreground dark:text-muted-foreground text-lg leading-relaxed">
                  {isTeacherMode ? (
                    <>
                      Your AI teaching assistant for <span className="font-semibold text-primary">{subject}</span>.
                      Here to help with student analytics and teaching insights.
                    </>
                  ) : (
                    <>
                      Your AI learning assistant for <span className="font-semibold text-primary">{subject}</span>.
                      Ready to help with concepts and study strategies.
                    </>
                  )}
                </p>
              </div>

              {/* Suggested Questions as Cards */}
              <div className="space-y-4">
                <p className="text-sm font-medium text-muted-foreground dark:text-muted-foreground">Try asking:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {getSuggestedQuestions().map((question, index) => (
                    <button
                      key={index}
                      onClick={async () => {
                        // Send the message immediately with the specific question
                        if (isLoading) return; // Prevent multiple clicks

                        const userMessage: Message = {
                          id: Date.now().toString(),
                          type: 'user',
                          content: question,
                          timestamp: new Date()
                        };

                        const loadingMessage: Message = {
                          id: `loading-${Date.now()}`,
                          type: 'assistant',
                          content: '',
                          timestamp: new Date(),
                          isLoading: true
                        };

                        // Update messages and clear input
                        setMessages(prev => [...prev, userMessage, loadingMessage]);
                        setInputValue('');
                        setIsLoading(true);

                        // Send the message using the helper function
                        await handleSendMessageWithContent(question);
                      }}
                      className="suggested-question-button text-left p-4 text-sm bg-background dark:bg-card border border-border dark:border-border rounded-xl hover:bg-primary/5 dark:hover:bg-primary/10 hover:border-primary/30 dark:hover:border-primary/40 hover:shadow-md transition-all duration-200 group"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-primary/60 dark:bg-primary/60 rounded-full mt-2 group-hover:bg-primary dark:group-hover:bg-primary transition-colors"></div>
                        <span className="leading-relaxed text-foreground dark:text-foreground">{question}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            /* Regular Chat Messages */
            <>
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-2 md:gap-3 items-center ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  {message.type === 'assistant' && message.isLoading && (
                    <div className="flex-shrink-0 w-7 h-7 md:w-8 md:h-8">
                      <Orb
                        hoverIntensity={0.8}
                        rotateOnHover={true}
                        hue={0}
                        forceHoverState={true}
                      />
                    </div>
                  )}

                  <div
                    className={`max-w-[85%] md:max-w-[80%] rounded-lg ${message.isLoading ? '' : 'px-3 py-2 md:px-4 md:py-3'} ${message.type === 'user'
                      ? 'bg-primary dark:bg-primary text-white dark:text-white ml-auto'
                      : message.isLoading ? '' : 'bg-muted dark:bg-muted text-foreground dark:text-foreground'
                      }`}
                  >
                    {message.isLoading ? (
                      <div className="flex items-center">
                        {/* <div className="flex space-x-1 w-8 h-8">
                          <div className="w-2 h-2 bg-primary/60 dark:bg-primary/80 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary/60 dark:bg-primary/80 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-primary/60 dark:bg-primary/80 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div> */}
                        <span className="text-sm text-muted-foreground dark:text-muted-foreground animate-pulse">
                          thinking...
                        </span>
                      </div>
                    ) : (
                      <div className={`enhanced-message-content ${message.type === 'user' ? 'user-message-content' : ''}`}>
                        <EnhancedMarkdown
                          text={message.content}
                          isUserMessage={message.type === 'user'}
                        />

                      </div>
                    )}

                    {!message.isLoading && (
                      <div className={`text-xs mt-1 md:mt-2 opacity-70 ${message.type === 'user'
                        ? 'text-white/70 dark:text-white/70'
                        : 'text-muted-foreground dark:text-muted-foreground'
                        }`}>
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    )}
                  </div>

                  {/* {message.type === 'user' && (
                    <div className="flex-shrink-0 w-7 h-7 md:w-8 md:h-8 bg-primary dark:bg-primary rounded-full flex items-center justify-center">
                      <UserIcon className="w-4 h-4 md:w-5 md:h-5 text-primary-foreground dark:text-primary-foreground" />
                    </div>
                  )} */}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Input Area - Styled to match sketch */}
        <div className="p-2 md:p-4 pb-16 rounded-t-2xl bg-background dark:bg-background">
          {/* Input field with subject switcher */}
          <div className="flex gap-2 mb-2">
            <div className="flex-1 relative" ref={subjectSwitcherRef}>
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={isTeacherMode ? `Ask about your ${subject} class...` : `Ask me about ${subject}...`}
                disabled={isLoading}
                className="pl-4 pr-24 h-11 md:h-12 text-base border-2 border-border dark:border-border focus:border-primary dark:focus:border-primary focus:ring-1 focus:ring-primary dark:focus:ring-primary rounded-xl bg-background dark:bg-input text-foreground dark:text-foreground placeholder:text-muted-foreground dark:placeholder:text-muted-foreground"
              />
              {onSubjectChange && availableSubjects.length > 0 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleSubjectSwitcher}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 px-2 flex items-center gap-1.5 text-sm rounded-lg border border-border dark:border-border hover:bg-muted/50 dark:hover:bg-muted/50"
                  >
                    <ArrowPathRoundedSquareIcon className="h-5 w-5 text-muted-foreground dark:text-muted-foreground" />
                    <span className="text-foreground dark:text-foreground hidden md:inline">{subject}</span>
                  </Button>

                  {/* Subject switcher dropdown */}
                  {showSubjectSwitcher && (
                    <div className="absolute right-2 bottom-full mb-1 bg-background dark:bg-popover border border-border dark:border-border rounded-lg shadow-lg dark:shadow-2xl z-50">
                      {availableSubjects.map((subjectOption) => (
                        <button
                          key={subjectOption.subjectName}
                          onClick={(e) => {
                            e.stopPropagation();
                            onSubjectChange(subjectOption.subjectName);
                            closeSubjectSwitcher();
                          }}
                          className={`w-full text-left px-3 py-2 hover:bg-muted dark:hover:bg-muted/50 transition-colors flex items-center text-sm ${subjectOption.subjectName === subject
                            ? 'bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary font-medium'
                            : 'text-foreground dark:text-foreground'
                            } ${availableSubjects.indexOf(subjectOption) === 0 ? 'first:rounded-t-lg' : ''
                            } ${availableSubjects.indexOf(subjectOption) === availableSubjects.length - 1 ? 'last:rounded-b-lg' : ''
                            }`}
                        >
                          {subjectOption.subjectName}
                        </button>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              size="lg"
              className="h-11 md:h-12 px-4 md:px-6 bg-primary hover:bg-primary/90 dark:bg-primary dark:hover:bg-primary/90 text-primary-foreground dark:text-primary-foreground rounded-xl transition-colors"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AegisAiChatbot;
