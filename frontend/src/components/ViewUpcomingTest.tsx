import React, { useState, ReactNode, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { AlertCircle, Calendar, Check<PERSON>he<PERSON>, CheckCircle, ClipboardList, Eye, Menu, MoreVertical, Pencil, Trash2, X } from 'lucide-react';
import { useUser } from '../contexts/userContext';
import TopicTagsInput from './TopicTagInput';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { replace, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ClipboardDocumentListIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    children: ReactNode;
}

interface TestItem {
    id: string;
    subject: string;
    topic: string; // Concatenated topics from the Topics array.
    testType: string;
    date: Date;
    duration: number;
    numOfQuestions: number;
    instructions: string;
    totalMarks: number;
    startTime?: string;
}

interface EditTestForm {
    date: Date;
    startTime: string;
    duration: number;
    totalMarks: number;
    passingMarks: number;
    instructions: string;
    testType: string;
}

interface GroupedTestItem extends TestItem {
    studentCount: number;
    allTestIds: string[];
}

const MenuDropdown = ({ children }: { children: React.ReactNode }) => {
    const [isOpen, setIsOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node) &&
                buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleToggle = () => {
        if (!isOpen && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownPosition({
                top: rect.bottom + window.scrollY + 4,
                right: window.innerWidth - rect.right - window.scrollX
            });
        }
        setIsOpen(!isOpen);
    };

    return (
        <>
            <button
                ref={buttonRef}
                onClick={handleToggle}
                className="p-1 hover:bg-accent/10 text-muted-foreground hover:text-accent rounded-full transition-colors"
            >
                <MoreVertical className="h-4 w-4" />
            </button>
            {isOpen && createPortal(
                <div
                    ref={menuRef}
                    className="fixed w-32 bg-card border border-border rounded-md shadow-lg z-50"
                    style={{
                        top: dropdownPosition.top,
                        right: dropdownPosition.right,
                    }}
                >
                    {children}
                </div>,
                document.body
            )}
        </>
    );
};

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
    if (!isOpen) return null;

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            onClose();
        }
    };

    return createPortal(
        <div
            className="fixed inset-0 z-50 flex items-center justify-center"
            role="dialog"
            aria-modal="true"
            onKeyDown={handleKeyDown}
        >
            <div className="fixed backdrop-blur-sm inset-0 bg-opacity-50"></div>
            <div
                className="bg-card rounded-lg z-50 p-6 w-full max-w-md relative border border-border shadow-lg m-4"
                onClick={(e) => e.stopPropagation()}
            >
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-muted-foreground hover:text-foreground"
                >
                    <X className="h-5 w-5" />
                </button>
                {children}
            </div>
        </div>,
        document.body
    );
};

const groupPersonalizedTests = (tests: TestItem[]): TestItem[] => {
    const normalTests = tests.filter(test => test.testType !== 'personalized');
    const personalizedTests = tests.filter(test => test.testType === 'personalized');

    // Group personalized tests by date, time, and topic
    const groupedTests = personalizedTests.reduce((acc: { [key: string]: GroupedTestItem }, test) => {
        const key = `${test.date.getTime()}_${test.startTime}_${test.topic}_${test.duration}`;

        if (!acc[key]) {
            acc[key] = {
                ...test,
                studentCount: 1,
                allTestIds: [test.id] // Initialize with first test ID
            };
        } else {
            acc[key].studentCount += 1;
            acc[key].allTestIds.push(test.id); // Add this test ID to the group
        }

        return acc;
    }, {});
    console.log("Grouped Personalized Tests:", groupedTests);

    return [...normalTests, ...Object.values(groupedTests)];
};

const ViewUpcomingTest = () => {
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [selectedTest, setSelectedTest] = useState<TestItem | null>(null);
    const navigate = useNavigate();

    // Start with an empty array; tests will be fetched from the API.
    const [upcomingTests, setUpcomingTests] = useState<TestItem[]>([]);

    const [editForm, setEditForm] = useState<EditTestForm>({
        date: new Date(),
        startTime: '',
        duration: 60,
        totalMarks: 100,
        passingMarks: 40,
        instructions: '',
        testType: 'generic'
    });

    const updateTestApi = async (selectedTest: TestItem, updatedTest: TestItem) => {
        const updateTestToast = toast.loading("Updating test...", { autoClose: false });
        try {
            const response = await axiosPrivate.put(`/api/test/update`, { testID: selectedTest?.id, testData: updatedTest });
            if (!response) {
                console.error('Network response was not ok');
            }
            toast.update(updateTestToast, {
                render: "Test updated successfully!",
                type: "success",
                isLoading: false,
                autoClose: 2000,
            });
        } catch (error) {
            console.error("Error updating test:", error);
            toast.update(updateTestToast, {
                render: "Failed to update test!",
                type: "error",
                isLoading: false,
                autoClose: 2000,
            });
        }
    };

    const deleteTestApi = async (testIds: string[]) => {
        console.log("Deleting tests with IDs:", testIds);
        const deleteTestToast = toast.loading("Deleting test(s)...", { autoClose: false });
        try {
            // Make parallel delete requests for all test IDs
            await Promise.all(
                testIds.map(testID =>
                    axiosPrivate.delete(`/api/test/delete`, { data: { testID } })
                )
            );

            toast.update(deleteTestToast, {
                render: "Test(s) deleted successfully!",
                type: "success",
                isLoading: false,
                autoClose: 2000,
            });
        } catch (error) {
            console.error("Error deleting tests:", error);
            toast.update(deleteTestToast, {
                render: "Failed to delete test(s)!",
                type: "error",
                isLoading: false,
                autoClose: 2000,
            });
        }
    };

    const isUpcomingTest = (test: any) => {
        // Convert test date to IST
        const testDate = new Date(test.testDate);
        const currentDate = new Date();

        // If no start time specified, test is not upcoming
        if (!test.startTime) {
            return false;
        }

        // Set test start time in IST
        const [hours, minutes] = test.startTime.split(':');
        testDate.setHours(parseInt(hours), parseInt(minutes));

        // Calculate test end time by adding duration (in minutes)
        const testEndTime = new Date(testDate.getTime() + (test.duration * 60 * 1000));

        // Consider a test upcoming if:
        // 1. It's active AND
        // 2. Current time is before the test end time (start time + duration)
        return (
            test.active &&
            currentDate <= testEndTime
        );
    };

    // Function to convert test data to TestItem format
    const formatTestData = (test: any): TestItem => ({
        id: test._id,
        subject: test.subject,
        topic: Array.isArray(test.topics)
            ? test.topics.join(', ')
            : test.topics,
        date: new Date(test.testDate),
        testType: test.testType,
        numOfQuestions: test.numberOfQuestions,
        duration: test.duration,
        instructions: test.testInstructions,
        totalMarks: test.totalMarks,
        startTime: test.startTime
    });

    // Handle teacher's upcoming tests
    useEffect(() => {
        if (user?.testHistory) {
            // console.error("User Test History:", user.testHistory);
            const tests = user.testHistory
                .filter(isUpcomingTest)
                .map(formatTestData)
                .sort((a, b) => a.date.getTime() - b.date.getTime());

            const groupedTests = groupPersonalizedTests(tests);
            setUpcomingTests(groupedTests);
        }
        // console.log("Upcoming Tests:", upcomingTests);
    }, [user?.testHistory]);

    // Handle student's upcoming tests
    useEffect(() => {
        if (user?.subjects) {
            const tests = user.subjects
                .flatMap(subject => subject.testHistory)
                .filter(test => test && isUpcomingTest(test))
                // Check if test ID exists in attemptedTests of any subject
                .filter((test: any) => !user.subjects?.some(subject =>
                    subject.attemptedTests.some(attempt => attempt.testId === test._id)
                ))
                .map(formatTestData)
                .sort((a, b) => a.date.getTime() - b.date.getTime());

            setUpcomingTests(tests);
        }
    }, [user?.subjects]);

    // Add a helper function to format date and time in IST
    const formatDateTimeIST = (date: Date, startTime?: string): string => {
        const testDate = new Date(date);
        if (startTime) {
            const [hours, minutes] = startTime.split(':');
            testDate.setHours(parseInt(hours), parseInt(minutes));
        }
        const options: Intl.DateTimeFormatOptions = {
            timeZone: 'Asia/Kolkata',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            hour12: true,
        };
        return new Intl.DateTimeFormat('en-US', options).format(testDate);
    };

    // Format date for input (YYYY-MM-DD)
    const formatDateForInput = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    function getLocalDate() {
        const now = new Date();
        now.setSeconds(0, 0); // Remove seconds and milliseconds.
        const offset = now.getTimezoneOffset();
        const localDate = new Date(now.getTime() - offset * 60 * 1000);
        return localDate.toISOString().slice(0, 10);
    }

    // Prevent body scroll when any modal is open
    useEffect(() => {
        if (viewModalOpen || editModalOpen || deleteModalOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [viewModalOpen, editModalOpen, deleteModalOpen]);

    const handleViewTest = (test: TestItem) => {
        setSelectedTest(test);
        setViewModalOpen(true);
    };

    const handleEditTest = (test: TestItem) => {
        setSelectedTest(test);
        // Directly mutate the editForm state to preserve input focus
        setEditForm(prev => ({
            ...prev,
            date: test.date,
            startTime: test.startTime || '',
            duration: test.duration,
            totalMarks: test.totalMarks,
            passingMarks: Math.floor(test.totalMarks * 0.4),
            instructions: test.instructions,
            testType: 'generic'
        }));

        setEditModalOpen(true);
    };

    const handleSaveEdit = () => {
        if (selectedTest) {
            const updatedTest = {
                ...selectedTest,
                date: editForm.date,
                startTime: editForm.startTime,
                duration: editForm.duration,
                totalMarks: editForm.totalMarks,
                instructions: editForm.instructions
            };

            setUpcomingTests(tests =>
                tests.map(test => test.id === selectedTest.id ? updatedTest : test)
            );
            updateTestApi(selectedTest, updatedTest);
            setEditModalOpen(false);
            setSelectedTest(null);
        }
    };

    const handleDeleteTest = (test: TestItem) => {
        setSelectedTest(test);
        setDeleteModalOpen(true);
    };

    const handleConfirmDelete = () => {
        if (selectedTest) {
            const testIdsToDelete = (selectedTest as GroupedTestItem).allTestIds || [selectedTest.id];
            setUpcomingTests(tests => tests.filter(test => !testIdsToDelete.includes(test.id)));
            deleteTestApi(testIdsToDelete);
            setDeleteModalOpen(false);
            setSelectedTest(null);
        }
    };

    const isTestAvailable = (test: TestItem): boolean => {
        const now = new Date();
        const testDate = new Date(test.date);

        // If no specific start time is set, return false
        if (!test.startTime) {
            return false;
        }

        // Set the test start time
        const [hours, minutes] = test.startTime.split(':');
        testDate.setHours(parseInt(hours), parseInt(minutes));

        // Calculate test end time
        const testEndTime = new Date(testDate.getTime() + (test.duration * 60 * 1000));

        // Test is available if current time is between start time and end time
        return now >= testDate && now <= testEndTime;
    };

    const isBeforeTestStart = (test: TestItem): boolean => {
        const now = new Date();
        const testDate = new Date(test.date);

        if (!test.startTime) {
            return true;
        }

        const [hours, minutes] = test.startTime.split(':');
        testDate.setHours(parseInt(hours), parseInt(minutes));

        return now < testDate;
    };

    return (
        <div className='grid grid-cols-1 h-full overflow-hidden'>
            {user?.role === 'Teacher' && (
                <div className="rounded-lg flex flex-col h-full">
                    <div className="p-2 border-b border-border flex items-center shrink-0">
                        <ClipboardDocumentListIcon className="h-4 w-4 mr-2 text-accent" />
                        <h2 className="text-lg font-['Space_Grotesk'] font-bold text-primary">Scheduled Tests</h2>
                    </div>
                    <div className="flex-1 p-2 overflow-auto min-h-0">
                        {upcomingTests.length > 0 ? (
                            upcomingTests.map(test => (
                                <div
                                    key={test.id}
                                    className="mb-3 p-3 w-auto h-auto text-sm bg-card hover:bg-accent/5 border border-border rounded-lg hover:shadow-sm transition duration-200"
                                >
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1 space-y-2 space-x-2">
                                            <span className="font-['Space_Grotesk'] font-medium text-primary">{test.subject[0].toUpperCase() + test.subject.slice(1)}</span>
                                            <span className={`text-xs px-2 py-0.5 rounded-full ${test.testType === 'personalized'
                                                ? 'bg-purple-100 text-purple-600'
                                                : test.testType === 'diagnostic'
                                                    ? 'bg-blue-100 text-blue-600'
                                                    : 'bg-green-100 text-green-600'
                                                }`}>
                                                {test.testType[0].toUpperCase() + test.testType.slice(1)}
                                            </span>
                                            <div className="flex flex-col mt-2 border-border border-t justify-between items-start"> {/* Modified this line */}
                                                <p className="text-xs text-foreground/70">{test.topic}</p>
                                                <span className="text-xs text-muted-foreground flex items-center">
                                                    <Calendar className="h-3 w-3 mr-1" />
                                                    <span className='whitespace-nowrap'>{formatDateTimeIST(test.date, test.startTime)}</span>
                                                </span>
                                            </div>
                                        </div>
                                        <MenuDropdown>
                                            <button
                                                className="w-full text-left px-3 py-2 text-sm hover:bg-accent/5 text-foreground"
                                                onClick={() => handleViewTest(test)}
                                            >
                                                View
                                            </button>
                                            <button
                                                className="w-full text-left px-3 py-2 text-sm hover:bg-accent/5 text-foreground"
                                                onClick={() => handleEditTest(test)}
                                            >
                                                Edit
                                            </button>
                                            <button
                                                className="w-full text-left px-3 py-2 text-sm hover:bg-destructive/5 text-destructive"
                                                onClick={() => handleDeleteTest(test)}
                                            >
                                                Delete
                                            </button>
                                        </MenuDropdown>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
                                <div className="bg-accent/10 rounded-full p-4 mb-4">
                                    <ExclamationCircleIcon className="h-6 w-6 text-accent" />
                                </div>
                                <h3 className="text-lg font-['Space_Grotesk'] font-medium text-primary mb-2">No Tests Found</h3>
                                <p className="text-sm text-foreground/70 max-w-xs">
                                    You have no upcoming tests scheduled. Create a new test from <span className='font-bold'>Create</span> tab to get started.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {user?.role === 'Student' && (
                <div className="bg-background rounded-lg border border-border">
                    <div className="p-2 space-y-3 overflow-auto">
                        {upcomingTests.length > 0 ? (
                            upcomingTests.map(test => {
                                const available = isTestAvailable(test);
                                const notStartedYet = isBeforeTestStart(test);
                                return (
                                    <div
                                        key={test.id}
                                        className="border border-border p-4 rounded-lg bg-card hover:bg-accent/5 transition"
                                    >
                                        <div className="flex flex-col sm:flex-row justify-between items-start">
                                            <div className="flex-1 space-y-2">
                                                <span className="font-['Space_Grotesk'] font-medium text-primary">{test.subject[0].toUpperCase() + test.subject.slice(1)}</span>
                                                {/* <div className='border-b border-border'/> */}
                                                <div className="flex flex-col mt-2 border-border border-t justify-between items-start"> {/* Modified this line */}
                                                    <p className="text-xs text-foreground/70">{test.topic}</p>
                                                    <span className="text-xs text-muted-foreground flex items-center">
                                                        <Calendar className="h-3 w-3 mr-1" />
                                                        <span className='whitespace-nowrap'>{formatDateTimeIST(test.date, test.startTime)}</span>
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 mt-3 sm:mt-0">
                                                <button
                                                    className="p-1 hover:bg-accent/10 text-muted-foreground hover:text-accent rounded-full transition-colors"
                                                    onClick={() => handleViewTest(test)}
                                                    title="View test"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </button>
                                                <button
                                                    className={`px-3 py-1 rounded-md text-sm font-medium transition ${available
                                                        ? 'bg-accent text-white hover:bg-accent/90'
                                                        : 'bg-muted text-muted-foreground cursor-not-allowed'
                                                        }`}
                                                    disabled={!available}
                                                    onClick={() => {
                                                        if (available) {
                                                            navigate("/test-instructions", { state: { testId: test.id, testMode: "scheduled" } });
                                                        }
                                                    }}
                                                >
                                                    {notStartedYet
                                                        ? "Not Started Yet"
                                                        : available
                                                            ? "Start Test"
                                                            : "Test Expired"}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
                                <div className="bg-accent/10 rounded-full p-4 mb-4">
                                    <CheckCircle className="h-6 w-6 text-accent" />
                                </div>
                                <h3 className="text-lg font-['Space_Grotesk'] font-medium text-primary mb-2">You're all caught up!</h3>
                                <p className="text-sm text-foreground/70 max-w-xs">
                                    Refresh the page to see if any new tests are available.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* View Test Modal */}
            <Modal isOpen={viewModalOpen} onClose={() => setViewModalOpen(false)}>
                {selectedTest && (
                    <div className="space-y-4">
                        <div className="flex items-center gap-3 mb-4">
                            <Eye className="h-5 w-5 text-accent" />
                            <h2 className="text-xl font-['Space_Grotesk'] font-bold text-primary">View Test</h2>
                        </div>
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Class</label>
                                    <p className="text-sm mt-1 text-primary">Class 10-A</p>
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Subject</label>
                                    <p className="text-sm mt-1 text-primary">{selectedTest.subject}</p>
                                </div>
                            </div>
                            <div className={`${user?.role === "Student" ? "hidden" : ""}`}>
                                <label className="block text-xs font-medium text-foreground/70">Test Type</label>
                                <p className="text-sm mt-1 text-primary">{selectedTest.testType.slice(0, 1).toUpperCase() + selectedTest.testType.slice(1)}</p>
                            </div>
                            <div className='grid grid-cols-2 gap-4'>
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Topic/Chapter</label>
                                    <p className="text-sm mt-1 text-primary">{selectedTest.topic}</p>
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Students</label>
                                    <p className="text-sm mt-1 text-primary">
                                        {(selectedTest as GroupedTestItem).studentCount || 'All'} Students
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Number of Questions</label>
                                    <p className="text-sm mt-1 text-primary">{selectedTest.numOfQuestions}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 gap-4">
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Date & Time (IST)</label>
                                    <p className="text-sm mt-1 text-primary">
                                        {formatDateTimeIST(selectedTest.date, selectedTest.startTime)}
                                    </p>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 gap-4">
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70">Duration</label>
                                    <p className="text-sm mt-1 text-primary">{selectedTest.duration} minutes</p>
                                </div>
                                {/* <div>
                                    <label className="block text-xs font-medium text-foreground/70">Total Marks</label>
                                    <p className="text-sm mt-1 text-primary">{selectedTest.totalMarks}</p>
                                </div> */}
                            </div>
                            <div>
                                <label className="block text-xs font-medium text-foreground/70">Test Instructions</label>
                                <p className="text-sm mt-1 text-primary">{selectedTest.instructions.length > 0 ? selectedTest.instructions : "None"}</p>
                            </div>
                        </div>
                    </div>
                )}
            </Modal>

            {/* Edit Test Modal */}
            <Modal isOpen={editModalOpen} onClose={() => setEditModalOpen(false)}>
                {selectedTest && (
                    <div className="space-y-4">
                        <div className="flex items-center gap-3 mb-4">
                            <Pencil className="h-5 w-5 text-accent" />
                            <h2 className="text-xl font-['Space_Grotesk'] font-bold text-primary">Edit Test</h2>
                        </div>
                        <form onSubmit={(e) => {
                            e.preventDefault();
                            handleSaveEdit();
                        }}>
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-xs font-medium text-foreground/70 mb-1">Date</label>
                                        <input
                                            type="date"
                                            min={getLocalDate()}
                                            className="w-full rounded border border-border bg-background p-2 text-sm focus:ring-2 focus:ring-accent"
                                            value={formatDateForInput(editForm.date)}
                                            onChange={(e) => setEditForm(prev => ({
                                                ...prev,
                                                date: new Date(e.target.value)
                                            }))}
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs font-medium text-foreground/70 mb-1">Start Time</label>
                                        <input
                                            type="time"
                                            className="w-full rounded border border-border bg-background p-2 text-sm focus:ring-2 focus:ring-accent"
                                            value={editForm.startTime}
                                            onChange={(e) => setEditForm(prev => ({
                                                ...prev,
                                                startTime: e.target.value
                                            }))}
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 gap-4">
                                    <div>
                                        <label className="block text-xs font-medium text-foreground/70 mb-1">Duration (min)</label>
                                        <input
                                            type="number"
                                            min="15"
                                            max="180"
                                            className="w-full rounded border border-border bg-background p-2 text-sm focus:ring-2 focus:ring-accent"
                                            value={editForm.duration}
                                            onChange={(e) => setEditForm(prev => ({
                                                ...prev,
                                                duration: parseInt(e.target.value)
                                            }))}
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-xs font-medium text-foreground/70 mb-1">Test Instructions</label>
                                    <textarea
                                        rows={2}
                                        name='instructions'
                                        className="w-full rounded border border-border bg-background p-2 text-sm focus:ring-2 focus:ring-accent"
                                        placeholder="Enter any specific instructions for students..."
                                        value={editForm.instructions}
                                        onChange={(e) => setEditForm(prev => ({
                                            ...prev,
                                            instructions: e.target.value
                                        }))}
                                    />
                                </div>
                                <button
                                    onClick={handleSaveEdit}
                                    className="w-full bg-accent text-white rounded-md py-2 hover:bg-accent/90 transition-colors"
                                >
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                )}
            </Modal>

            {/* Delete Test Modal */}
            <Modal isOpen={deleteModalOpen} onClose={() => setDeleteModalOpen(false)}>
                {selectedTest && (
                    <div className="space-y-4">
                        <div className="flex items-center gap-3 mb-4">
                            <Trash2 className="h-5 w-5 text-destructive" />
                            <h2 className="text-xl font-['Space_Grotesk'] font-bold text-primary">Delete Test</h2>
                        </div>

                        <p className="text-foreground">
                            Are you sure you want to delete the
                            <span className='font-bold'> {selectedTest.subject} {selectedTest.testType === 'personalized' ? 'personalized ' : ''}test
                                on {selectedTest.topic}</span>
                            {selectedTest.testType === 'personalized' && (selectedTest as GroupedTestItem).studentCount > 1
                                ? ` for ${(selectedTest as GroupedTestItem).studentCount} students`
                                : ''}?
                            This action cannot be undone.
                        </p>
                        <div className="flex gap-4 pt-4">
                            <button
                                onClick={() => setDeleteModalOpen(false)}
                                className="flex-1 border border-border rounded-md py-2 hover:bg-muted transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleConfirmDelete}
                                className="flex-1 bg-destructive text-destructive-foreground rounded-md py-2 hover:bg-destructive/90 transition-colors"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default ViewUpcomingTest;