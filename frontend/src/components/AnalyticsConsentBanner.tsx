import React, { useState, useEffect } from 'react';
import { useAnalytics } from '../contexts/analyticsContext';
import { XMarkIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON> } from 'lucide-react';

interface ConsentPreferences {
    analytics: boolean;
    performance: boolean;
    functional: boolean;
}

const AnalyticsConsentBanner: React.FC = () => {
    const { setAnalyticsEnabled, isEnabled } = useAnalytics();
    const [showBanner, setShowBanner] = useState(false);
    const [showDetails, setShowDetails] = useState(false);
    const [preferences, setPreferences] = useState<ConsentPreferences>({
        analytics: true,
        performance: true,
        functional: true
    });

    useEffect(() => {
        // Check if user has already made a consent choice
        const consentGiven = localStorage.getItem('analytics_consent');
        const consentTimestamp = localStorage.getItem('analytics_consent_timestamp');
        
        if (!consentGiven) {
            // Show banner if no consent has been given
            setShowBanner(true);
        } else {
            // Check if consent is older than 1 year (re-consent required)
            const oneYearAgo = Date.now() - (365 * 24 * 60 * 60 * 1000);
            if (consentTimestamp && parseInt(consentTimestamp) < oneYearAgo) {
                setShowBanner(true);
            } else {
                // Apply saved consent
                const savedConsent = JSON.parse(consentGiven);
                setAnalyticsEnabled(savedConsent.analytics);
                setPreferences(savedConsent);
            }
        }
    }, [setAnalyticsEnabled]);

    const handleAcceptAll = () => {
        console.log('Analytics Consent: User accepted all analytics');
        const consent = {
            analytics: true,
            performance: true,
            functional: true
        };

        saveConsent(consent);
        setAnalyticsEnabled(true);
        setShowBanner(false);
        console.log('Analytics Consent: Analytics enabled, banner hidden');
    };

    const handleRejectAll = () => {
        const consent = {
            analytics: false,
            performance: false,
            functional: true // Keep functional cookies for basic site operation
        };
        
        saveConsent(consent);
        setAnalyticsEnabled(false);
        setShowBanner(false);
    };

    const handleSavePreferences = () => {
        saveConsent(preferences);
        setAnalyticsEnabled(preferences.analytics);
        setShowBanner(false);
        setShowDetails(false);
    };

    const saveConsent = (consent: ConsentPreferences) => {
        console.log('Analytics Consent: Saving consent to localStorage:', consent);
        localStorage.setItem('analytics_consent', JSON.stringify(consent));
        localStorage.setItem('analytics_consent_timestamp', Date.now().toString());
        console.log('Analytics Consent: Consent saved successfully');
    };

    const handlePreferenceChange = (key: keyof ConsentPreferences, value: boolean) => {
        setPreferences(prev => ({
            ...prev,
            [key]: value
        }));
    };

    if (!showBanner) {
        return null;
    }

    return (
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border shadow-lg">
            <div className="max-w-7xl mx-auto p-4">
                {!showDetails ? (
                    // Simple consent banner
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div className="flex items-start gap-3 flex-1">
                            <Cookie className="w-6 h-6 text-accent flex-shrink-0 mt-0.5" />
                            <div>
                                <h3 className="text-sm font-semibold text-primary mb-1">
                                    We use analytics to improve your experience
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                    We collect anonymous usage data to understand how you use our platform and make improvements. 
                                    No personal information is stored. You can change your preferences at any time.
                                </p>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-3 flex-shrink-0">
                            <button
                                onClick={() => setShowDetails(true)}
                                className="text-sm text-accent hover:text-accent/80 underline transition-colors"
                            >
                                Customize
                            </button>
                            <button
                                onClick={handleRejectAll}
                                className="px-4 py-2 text-sm border border-border rounded-md text-primary hover:bg-accent/10 transition-colors"
                            >
                                Reject All
                            </button>
                            <button
                                onClick={handleAcceptAll}
                                className="px-4 py-2 text-sm bg-accent text-white rounded-md hover:bg-accent/90 transition-colors"
                            >
                                Accept All
                            </button>
                        </div>
                    </div>
                ) : (
                    // Detailed preferences
                    <div className="space-y-6">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold text-primary">
                                Privacy Preferences
                            </h3>
                            <button
                                onClick={() => setShowDetails(false)}
                                className="p-1 text-muted-foreground hover:text-primary transition-colors"
                            >
                                <XMarkIcon className="w-5 h-5" />
                            </button>
                        </div>

                        <div className="grid gap-4">
                            {/* Functional Cookies */}
                            <div className="flex items-start justify-between p-4 bg-background rounded-lg border border-border">
                                <div className="flex-1">
                                    <h4 className="font-medium text-primary mb-1">Functional</h4>
                                    <p className="text-sm text-muted-foreground">
                                        Essential for basic site functionality. These cannot be disabled.
                                    </p>
                                </div>
                                <div className="ml-4">
                                    <div className="w-12 h-6 bg-accent rounded-full flex items-center justify-end px-1">
                                        <div className="w-4 h-4 bg-white rounded-full"></div>
                                    </div>
                                </div>
                            </div>

                            {/* Analytics Cookies */}
                            <div className="flex items-start justify-between p-4 bg-background rounded-lg border border-border">
                                <div className="flex-1">
                                    <h4 className="font-medium text-primary mb-1">Analytics</h4>
                                    <p className="text-sm text-muted-foreground">
                                        Help us understand how you use our platform to improve your experience. 
                                        All data is anonymous and aggregated.
                                    </p>
                                    <ul className="text-xs text-muted-foreground mt-2 space-y-1">
                                        <li>• Page views and navigation patterns</li>
                                        <li>• Feature usage statistics</li>
                                        <li>• Performance metrics</li>
                                        <li>• Error tracking for improvements</li>
                                    </ul>
                                </div>
                                <div className="ml-4">
                                    <button
                                        onClick={() => handlePreferenceChange('analytics', !preferences.analytics)}
                                        className={`w-12 h-6 rounded-full flex items-center transition-colors ${
                                            preferences.analytics 
                                                ? 'bg-accent justify-end' 
                                                : 'bg-gray-300 dark:bg-gray-600 justify-start'
                                        }`}
                                    >
                                        <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                                    </button>
                                </div>
                            </div>

                            {/* Performance Cookies */}
                            <div className="flex items-start justify-between p-4 bg-background rounded-lg border border-border">
                                <div className="flex-1">
                                    <h4 className="font-medium text-primary mb-1">Performance</h4>
                                    <p className="text-sm text-muted-foreground">
                                        Monitor site performance and loading times to ensure optimal user experience.
                                    </p>
                                    <ul className="text-xs text-muted-foreground mt-2 space-y-1">
                                        <li>• Page load times</li>
                                        <li>• API response times</li>
                                        <li>• Error rates and debugging</li>
                                    </ul>
                                </div>
                                <div className="ml-4">
                                    <button
                                        onClick={() => handlePreferenceChange('performance', !preferences.performance)}
                                        className={`w-12 h-6 rounded-full flex items-center transition-colors ${
                                            preferences.performance 
                                                ? 'bg-accent justify-end' 
                                                : 'bg-gray-300 dark:bg-gray-600 justify-start'
                                        }`}
                                    >
                                        <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-border">
                            <p className="text-xs text-muted-foreground">
                                You can change these preferences at any time in your account settings.
                            </p>
                            <div className="flex gap-3">
                                <button
                                    onClick={handleRejectAll}
                                    className="px-4 py-2 text-sm border border-border rounded-md text-primary hover:bg-accent/10 transition-colors"
                                >
                                    Reject All
                                </button>
                                <button
                                    onClick={handleSavePreferences}
                                    className="px-4 py-2 text-sm bg-accent text-white rounded-md hover:bg-accent/90 transition-colors"
                                >
                                    Save Preferences
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AnalyticsConsentBanner;
