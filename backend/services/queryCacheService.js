import LLMCache from '../models/LLMCacheModel.js';
import crypto from 'crypto';

/**
 * Query Cache Service
 * Provides intelligent caching for the text-to-MongoDB query system
 * with query-specific optimization and 7-day TTL
 */
class QueryCacheService {
  constructor() {
    this.cacheConfig = this.initializeCacheConfig();
    this.performanceMetrics = {
      hits: 0,
      misses: 0,
      totalQueries: 0,
      avgResponseTime: 0
    };
  }

  /**
   * Check cache for existing query results
   * @param {string} userQuery - Original user query
   * @param {string} userId - User ID
   * @param {string} subject - Subject context
   * @param {Array} generatedQueries - Generated MongoDB queries
   * @returns {Promise<Object|null>} Cached results or null
   */
  async checkQueryCache(userQuery, userId, subject, generatedQueries) {
    try {
      const cacheKey = this.generateQueryCacheKey(userQuery, userId, subject, generatedQueries);
      
      const cachedEntry = await LLMCache.findOne({ 
        cacheKey,
        requestType: 'query_system'
      });

      if (cachedEntry && this.isCacheValid(cachedEntry)) {
        await cachedEntry.incrementHitCount();
        this.performanceMetrics.hits++;
        
        console.log(`[QUERY-CACHE] Cache hit for user ${userId}, query: "${userQuery.substring(0, 50)}..."`);
        
        return {
          success: true,
          cached: true,
          results: cachedEntry.response.results,
          metadata: cachedEntry.response.metadata,
          cacheInfo: {
            createdAt: cachedEntry.createdAt,
            hitCount: cachedEntry.hitCount,
            lastAccessed: new Date()
          }
        };
      }

      this.performanceMetrics.misses++;
      return null;

    } catch (error) {
      console.error('[QUERY-CACHE] Error checking cache:', error);
      return null;
    }
  }

  /**
   * Store query results in cache
   * @param {string} userQuery - Original user query
   * @param {string} userId - User ID
   * @param {string} subject - Subject context
   * @param {Array} generatedQueries - Generated MongoDB queries
   * @param {Object} queryResults - Query execution results
   * @param {Object} contextResults - Context injection results
   * @param {Object} metadata - Additional metadata
   */
  async storeQueryCache(userQuery, userId, subject, generatedQueries, queryResults, contextResults, metadata = {}) {
    try {
      const cacheKey = this.generateQueryCacheKey(userQuery, userId, subject, generatedQueries);
      const messageHash = this.generateMessageHash(userQuery);

      // Prepare cache data
      const cacheData = {
        results: queryResults.results,
        metadata: {
          ...queryResults.metadata,
          ...metadata,
          contextSize: contextResults.metadata?.contextSize || 0,
          collectionsAccessed: queryResults.summary?.collectionsAccessed || [],
          queriesExecuted: queryResults.summary?.totalQueries || 0,
          documentsRetrieved: queryResults.summary?.totalDocuments || 0
        }
      };

      // Create cache entry
      const cacheEntry = new LLMCache({
        studentId: userId,
        subject,
        requestType: 'query_system',
        conversationId: null, // Query cache is independent of conversations
        messageHash,
        cacheKey,
        response: cacheData,
        metadata: {
          querySystemVersion: '1.0',
          cacheType: 'query_results',
          originalQueryLength: userQuery.length,
          queriesGenerated: generatedQueries.length,
          ...metadata
        }
      });

      await cacheEntry.save();
      
      console.log(`[QUERY-CACHE] Cached results for user ${userId}, key: ${cacheKey.substring(0, 20)}...`);
      
      // Update performance metrics
      this.performanceMetrics.totalQueries++;
      
    } catch (error) {
      console.error('[QUERY-CACHE] Error storing cache:', error);
    }
  }

  /**
   * Generate cache key for query results
   */
  generateQueryCacheKey(userQuery, userId, subject, generatedQueries) {
    // Create a deterministic key based on query content and structure
    const querySignature = this.generateQuerySignature(generatedQueries);
    const contentHash = crypto.createHash('sha256')
      .update(userQuery + userId + subject + querySignature)
      .digest('hex');
    
    return `query_${userId}_${subject}_${contentHash.substring(0, 16)}`;
  }

  /**
   * Generate signature for MongoDB queries to detect similar query patterns
   */
  generateQuerySignature(queries) {
    const signature = queries.map(query => {
      return {
        collection: query.collection,
        operation: query.operation,
        queryStructure: this.getQueryStructure(query.query || {}),
        projectionFields: Object.keys(query.projection || {}),
        hasSort: !!query.sort,
        hasLimit: !!query.limit
      };
    });
    
    return JSON.stringify(signature);
  }

  /**
   * Get query structure for caching (without specific values)
   */
  getQueryStructure(queryObj) {
    const structure = {};
    
    Object.keys(queryObj).forEach(key => {
      if (typeof queryObj[key] === 'object' && queryObj[key] !== null) {
        structure[key] = this.getQueryStructure(queryObj[key]);
      } else {
        structure[key] = typeof queryObj[key]; // Store type instead of value
      }
    });
    
    return structure;
  }

  /**
   * Generate message hash for deduplication
   */
  generateMessageHash(message) {
    return crypto.createHash('md5').update(message.toLowerCase().trim()).digest('hex');
  }

  /**
   * Check if cache entry is still valid
   */
  isCacheValid(cacheEntry) {
    const now = new Date();
    const cacheAge = now - cacheEntry.createdAt;
    const maxAge = this.cacheConfig.ttl;
    
    return cacheAge < maxAge;
  }

  /**
   * Intelligent cache warming for common queries
   * @param {string} userId - User ID
   * @param {string} subject - Subject to warm cache for
   */
  async warmCache(userId, subject) {
    try {
      const commonQueries = this.getCommonQueries(subject);
      
      console.log(`[QUERY-CACHE] Warming cache for user ${userId}, subject ${subject}`);
      
      // This would typically be called during off-peak hours
      // For now, just log the intent
      console.log(`[QUERY-CACHE] Would warm ${commonQueries.length} common queries`);
      
    } catch (error) {
      console.error('[QUERY-CACHE] Error warming cache:', error);
    }
  }

  /**
   * Get common queries for cache warming
   */
  getCommonQueries(subject) {
    return [
      `How am I performing in ${subject}?`,
      `What are my weak areas in ${subject}?`,
      `Show my recent test results in ${subject}`,
      `What topics should I focus on in ${subject}?`,
      `How is my learning progress in ${subject}?`
    ];
  }

  /**
   * Clean expired cache entries
   */
  async cleanExpiredCache() {
    try {
      const expiredDate = new Date(Date.now() - this.cacheConfig.ttl);
      
      const result = await LLMCache.deleteMany({
        requestType: 'query_system',
        createdAt: { $lt: expiredDate }
      });
      
      console.log(`[QUERY-CACHE] Cleaned ${result.deletedCount} expired cache entries`);
      
      return result.deletedCount;
      
    } catch (error) {
      console.error('[QUERY-CACHE] Error cleaning expired cache:', error);
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const hitRate = this.performanceMetrics.totalQueries > 0 
      ? (this.performanceMetrics.hits / this.performanceMetrics.totalQueries * 100).toFixed(2)
      : 0;

    return {
      ...this.performanceMetrics,
      hitRate: `${hitRate}%`,
      missRate: `${(100 - hitRate).toFixed(2)}%`
    };
  }

  /**
   * Invalidate cache for specific user/subject
   * @param {string} userId - User ID
   * @param {string} subject - Subject (optional)
   */
  async invalidateUserCache(userId, subject = null) {
    try {
      const query = {
        studentId: userId,
        requestType: 'query_system'
      };
      
      if (subject) {
        query.subject = subject;
      }
      
      const result = await LLMCache.deleteMany(query);
      
      console.log(`[QUERY-CACHE] Invalidated ${result.deletedCount} cache entries for user ${userId}`);
      
      return result.deletedCount;
      
    } catch (error) {
      console.error('[QUERY-CACHE] Error invalidating cache:', error);
      return 0;
    }
  }

  /**
   * Get cache entries for analysis
   * @param {string} userId - User ID
   * @param {number} limit - Number of entries to return
   */
  async getCacheEntries(userId, limit = 10) {
    try {
      return await LLMCache.find({
        studentId: userId,
        requestType: 'query_system'
      })
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('cacheKey createdAt hitCount metadata.collectionsAccessed metadata.queriesExecuted');
      
    } catch (error) {
      console.error('[QUERY-CACHE] Error getting cache entries:', error);
      return [];
    }
  }

  /**
   * Optimize cache performance by analyzing usage patterns
   */
  async optimizeCache() {
    try {
      // Analyze cache hit patterns
      const cacheAnalysis = await LLMCache.aggregate([
        { $match: { requestType: 'query_system' } },
        {
          $group: {
            _id: '$subject',
            totalEntries: { $sum: 1 },
            totalHits: { $sum: '$hitCount' },
            avgHits: { $avg: '$hitCount' }
          }
        },
        { $sort: { totalHits: -1 } }
      ]);

      console.log('[QUERY-CACHE] Cache analysis:', cacheAnalysis);

      // Remove low-hit entries older than 1 day
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const cleanupResult = await LLMCache.deleteMany({
        requestType: 'query_system',
        hitCount: { $lte: 1 },
        createdAt: { $lt: oneDayAgo }
      });

      console.log(`[QUERY-CACHE] Optimized cache: removed ${cleanupResult.deletedCount} low-hit entries`);

      return {
        analysis: cacheAnalysis,
        optimized: cleanupResult.deletedCount
      };

    } catch (error) {
      console.error('[QUERY-CACHE] Error optimizing cache:', error);
      return null;
    }
  }

  /**
   * Initialize cache configuration
   */
  initializeCacheConfig() {
    return {
      ttl: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      maxEntries: 10000, // Maximum cache entries per user
      cleanupInterval: 24 * 60 * 60 * 1000, // Daily cleanup
      warmupQueries: 5, // Number of queries to warm up
      hitThreshold: 2 // Minimum hits to keep entry during optimization
    };
  }
}

export default new QueryCacheService();
