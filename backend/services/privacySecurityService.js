import mongoose from 'mongoose';
import crypto from 'crypto';

/**
 * Privacy and Security Service
 * Ensures compliance with DPDP Act 2023 and maintains data protection standards
 * for the text-to-MongoDB query system
 */
class PrivacySecurityService {
  constructor() {
    this.sensitiveFields = this.initializeSensitiveFields();
    this.accessControlRules = this.initializeAccessControlRules();
    this.dataMinimizationRules = this.initializeDataMinimizationRules();
    this.auditLog = [];
  }

  /**
   * Validate and sanitize a MongoDB query before execution
   * @param {Object} query - MongoDB query object
   * @param {string} userType - 'student' or 'teacher'
   * @param {string} userId - User ID for access control
   * @param {string} collection - Target collection name
   * @returns {Object} Validated and sanitized query
   */
  validateAndSanitizeQuery(query, userType, userId, collection) {
    try {
      // Step 1: Validate access permissions
      this.validateCollectionAccess(collection, userType);

      // Step 2: Apply data minimization
      const sanitizedQuery = this.applyDataMinimization(query, collection, userType);

      // Step 3: Add mandatory filters
      const secureQuery = this.addMandatoryFilters(sanitizedQuery, userType, userId, collection);

      // Step 4: Validate query structure
      this.validateQueryStructure(secureQuery);

      // Step 5: Log access attempt
      this.logDataAccess(userType, userId, collection, 'query_validation', true);

      return secureQuery;

    } catch (error) {
      this.logDataAccess(userType, userId, collection, 'query_validation', false, error.message);
      throw new Error(`Privacy validation failed: ${error.message}`);
    }
  }

  /**
   * Sanitize query results to remove sensitive data
   * @param {Array|Object} results - Query results
   * @param {string} collection - Source collection
   * @param {string} userType - User type for access control
   * @returns {Array|Object} Sanitized results
   */
  sanitizeQueryResults(results, collection, userType) {
    if (!results) return results;

    const sanitizedResults = Array.isArray(results) ? results : [results];
    
    return sanitizedResults.map(doc => {
      const sanitized = this.deepClone(doc);
      
      // Remove sensitive fields
      this.removeSensitiveFields(sanitized, collection);
      
      // Anonymize ObjectIds
      this.anonymizeObjectIds(sanitized);
      
      // Apply collection-specific sanitization
      this.applyCollectionSanitization(sanitized, collection, userType);
      
      // Remove metadata that could expose system internals
      this.removeSystemMetadata(sanitized);
      
      return sanitized;
    });
  }

  /**
   * Validate access to specific collections based on user type
   */
  validateCollectionAccess(collection, userType) {
    const allowedCollections = this.accessControlRules[userType];
    
    if (!allowedCollections || !allowedCollections.includes(collection)) {
      throw new Error(`Access denied: ${userType} cannot access ${collection} collection`);
    }

    // Special case: Block teacher access to chat conversations
    if (collection === 'chatConversations' && userType === 'teacher') {
      throw new Error('Teachers cannot access student chat conversations for privacy protection');
    }
  }

  /**
   * Apply data minimization principles
   */
  applyDataMinimization(query, collection, userType) {
    const minimizedQuery = { ...query };

    // Ensure projection limits data exposure
    if (!minimizedQuery.projection) {
      minimizedQuery.projection = this.getMinimalProjection(collection, userType);
    } else {
      // Merge with minimal projection to ensure sensitive fields are excluded
      minimizedQuery.projection = {
        ...minimizedQuery.projection,
        ...this.getMinimalProjection(collection, userType)
      };
    }

    // Apply result limits
    if (!minimizedQuery.limit || minimizedQuery.limit > this.dataMinimizationRules.maxDocuments) {
      minimizedQuery.limit = this.dataMinimizationRules.maxDocuments;
    }

    return minimizedQuery;
  }

  /**
   * Add mandatory filters for user isolation
   */
  addMandatoryFilters(query, userType, userId, collection) {
    const secureQuery = { ...query };

    switch (collection) {
      case 'students':
        if (userType === 'student') {
          secureQuery.query = { ...query.query, username: userId };
        }
        break;

      case 'studentKnowledgeGraph':
        if (userType === 'student') {
          secureQuery.query = { ...query.query, studentId: userId };
        }
        break;

      case 'chatConversations':
        if (userType === 'student') {
          secureQuery.query = { ...query.query, userId };
        }
        break;

      case 'aegisGrader':
        if (userType === 'student') {
          secureQuery.query = {
            ...query.query,
            'answerSheets.rollNumber': userId
          };
        }
        break;
    }

    return secureQuery;
  }

  /**
   * Validate query structure for security
   */
  validateQueryStructure(query) {
    // Check for potentially dangerous operations
    if (query.query && typeof query.query === 'object') {
      this.validateQueryOperators(query.query);
    }

    // Validate aggregation pipelines
    if (query.pipeline && Array.isArray(query.pipeline)) {
      this.validateAggregationPipeline(query.pipeline);
    }

    // Ensure no system collections are targeted
    this.validateCollectionName(query.collection);
  }

  /**
   * Validate MongoDB query operators
   */
  validateQueryOperators(queryObj) {
    const dangerousOperators = ['$where', '$expr', '$function'];
    
    const checkOperators = (obj) => {
      if (typeof obj !== 'object' || obj === null) return;
      
      Object.keys(obj).forEach(key => {
        if (dangerousOperators.includes(key)) {
          throw new Error(`Dangerous query operator not allowed: ${key}`);
        }
        if (typeof obj[key] === 'object') {
          checkOperators(obj[key]);
        }
      });
    };

    checkOperators(queryObj);
  }

  /**
   * Validate aggregation pipeline stages
   */
  validateAggregationPipeline(pipeline) {
    const dangerousStages = ['$out', '$merge', '$function'];
    
    pipeline.forEach((stage, index) => {
      const stageKeys = Object.keys(stage);
      stageKeys.forEach(stageKey => {
        if (dangerousStages.includes(stageKey)) {
          throw new Error(`Dangerous aggregation stage not allowed: ${stageKey}`);
        }
      });
    });
  }

  /**
   * Validate collection name
   */
  validateCollectionName(collection) {
    const systemCollections = ['admin', 'config', 'local'];
    if (systemCollections.includes(collection)) {
      throw new Error(`System collection access not allowed: ${collection}`);
    }
  }

  /**
   * Remove sensitive fields from documents
   */
  removeSensitiveFields(doc, collection) {
    const sensitiveFields = this.sensitiveFields[collection] || this.sensitiveFields.global;
    
    sensitiveFields.forEach(field => {
      if (field.includes('.')) {
        this.removeNestedField(doc, field);
      } else {
        delete doc[field];
      }
    });
  }

  /**
   * Remove nested fields
   */
  removeNestedField(obj, fieldPath) {
    const parts = fieldPath.split('.');
    let current = obj;
    
    for (let i = 0; i < parts.length - 1; i++) {
      if (current[parts[i]]) {
        current = current[parts[i]];
      } else {
        return; // Path doesn't exist
      }
    }
    
    if (current && current[parts[parts.length - 1]]) {
      delete current[parts[parts.length - 1]];
    }
  }

  /**
   * Anonymize ObjectIds to prevent system exposure
   */
  anonymizeObjectIds(doc) {
    const anonymizeRecursive = (obj) => {
      if (!obj || typeof obj !== 'object') return;
      
      Object.keys(obj).forEach(key => {
        if (mongoose.Types.ObjectId.isValid(obj[key])) {
          // Replace ObjectId with anonymized reference
          obj[key] = this.generateAnonymizedId(obj[key].toString());
        } else if (Array.isArray(obj[key])) {
          obj[key].forEach(item => anonymizeRecursive(item));
        } else if (typeof obj[key] === 'object') {
          anonymizeRecursive(obj[key]);
        }
      });
    };

    anonymizeRecursive(doc);
  }

  /**
   * Generate anonymized ID
   */
  generateAnonymizedId(originalId) {
    return crypto.createHash('sha256')
      .update(originalId + process.env.PRIVACY_SALT || 'default_salt')
      .digest('hex')
      .substring(0, 12);
  }

  /**
   * Apply collection-specific sanitization
   */
  applyCollectionSanitization(doc, collection, userType) {
    switch (collection) {
      case 'students':
        if (userType === 'teacher') {
          // Remove personal identifiers for teachers
          delete doc.email;
          delete doc.admissionNumber;
          delete doc.profileImage;
        }
        break;

      case 'testHistory':
        // Remove detailed question content
        if (doc.questions) {
          doc.questionCount = doc.questions.length;
          delete doc.questions;
        }
        break;

      case 'aegisGrader':
        // Remove detailed answer sheets for privacy
        if (doc.answerSheets && userType === 'teacher') {
          doc.answerSheetCount = doc.answerSheets.length;
          delete doc.answerSheets;
        }
        break;
    }
  }

  /**
   * Remove system metadata
   */
  removeSystemMetadata(doc) {
    const systemFields = ['__v', '_id', 'createdAt', 'updatedAt'];
    systemFields.forEach(field => delete doc[field]);
  }

  /**
   * Log data access for audit purposes
   */
  logDataAccess(userType, userId, collection, operation, success, error = null) {
    const logEntry = {
      timestamp: new Date(),
      userType,
      userId,
      collection,
      operation,
      success,
      error,
      ipAddress: 'masked', // IP should be masked for privacy
      sessionId: 'masked'   // Session ID should be masked
    };

    this.auditLog.push(logEntry);
    
    // Keep only last 1000 entries in memory
    if (this.auditLog.length > 1000) {
      this.auditLog = this.auditLog.slice(-1000);
    }

    // In production, this should be written to a secure audit log
    console.log(`[PRIVACY-AUDIT] ${JSON.stringify(logEntry)}`);
  }

  /**
   * Get minimal projection for data minimization
   */
  getMinimalProjection(collection, userType) {
    const projections = {
      students: {
        password: 0,
        securityToken: 0,
        email: userType === 'teacher' ? 0 : 1,
        'subjects.attemptedTests.metadata': 0
      },
      testHistory: {
        questions: 0, // Exclude for performance and privacy
        metadata: 0
      },
      aegisGrader: {
        'answerSheets.metadata': 0,
        'answerSheets.responses': userType === 'teacher' ? 0 : 1
      },
      chatConversations: {
        'messages.metadata': 0
      }
    };

    return projections[collection] || {};
  }

  /**
   * Deep clone object with circular reference protection
   */
  deepClone(obj, visited = new WeakSet()) {
    // Handle null, undefined, and primitive types
    if (obj === null || typeof obj !== 'object') return obj;

    // Handle Date objects
    if (obj instanceof Date) return new Date(obj);

    // Handle Mongoose documents by converting to plain object first
    if (obj.toObject && typeof obj.toObject === 'function') {
      obj = obj.toObject();
    }

    // Check for circular references
    if (visited.has(obj)) {
      return {}; // Return empty object for circular references
    }

    // Add to visited set
    visited.add(obj);

    // Handle arrays
    if (Array.isArray(obj)) {
      const clonedArray = obj.map(item => this.deepClone(item, visited));
      visited.delete(obj); // Clean up visited set
      return clonedArray;
    }

    // Handle objects
    const cloned = {};
    Object.keys(obj).forEach(key => {
      try {
        cloned[key] = this.deepClone(obj[key], visited);
      } catch (error) {
        // Skip problematic properties that cause circular references
        console.warn(`[PRIVACY-SERVICE] Skipping property ${key} due to circular reference`);
      }
    });

    // Clean up visited set
    visited.delete(obj);

    return cloned;
  }

  /**
   * Get audit log (for admin purposes)
   */
  getAuditLog(limit = 100) {
    return this.auditLog.slice(-limit);
  }

  /**
   * Initialize sensitive fields configuration
   */
  initializeSensitiveFields() {
    return {
      global: ['password', 'securityToken', '__v', '_id'],
      students: [
        'password', 'securityToken', 'email', 'metadata.ipAddress',
        'metadata.device', 'metadata.browser', 'subjects.attemptedTests.metadata'
      ],
      testHistory: ['questions', 'metadata'],
      aegisGrader: ['answerSheets.metadata', 'metadata'],
      chatConversations: ['messages.metadata', 'metadata']
    };
  }

  /**
   * Initialize access control rules
   */
  initializeAccessControlRules() {
    return {
      student: [
        'students', 'studentKnowledgeGraph', 'testHistory',
        'aegisGrader', 'chatConversations', 'curriculumNodes',
        'questions', 'aiQuestions', 'questionTemp'
      ],
      teacher: [
        'students', 'studentKnowledgeGraph', 'testHistory',
        'aegisGrader', 'curriculumNodes', 'questions',
        'aiQuestions', 'questionTemp'
        // Note: chatConversations is explicitly excluded
      ]
    };
  }

  /**
   * Initialize data minimization rules
   */
  initializeDataMinimizationRules() {
    return {
      maxDocuments: 100,
      maxQueryTime: 5000, // 5 seconds
      maxProjectionFields: 20,
      requireProjection: true
    };
  }
}

export default new PrivacySecurityService();
