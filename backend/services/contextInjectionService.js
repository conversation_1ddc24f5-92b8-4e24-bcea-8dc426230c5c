import { GoogleGenerativeA<PERSON> } from '@google/generative-ai';
import { config } from 'dotenv';

config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const contextProcessingModel = "gemini-2.0-flash";

/**
 * Context Injection Service
 * Processes query results and intelligently injects them as context into LLM prompts
 * Replaces the current approach of sending complete user data
 */
class ContextInjectionService {
  constructor() {
    this.contextTemplates = this.initializeContextTemplates();
    this.relevanceThresholds = this.initializeRelevanceThresholds();
    this.summarizationRules = this.initializeSummarizationRules();
  }

  /**
   * Main method to process query results and generate enhanced context
   * @param {Array} queryResults - Results from queryExecutionEngine
   * @param {string} userQuery - Original user query
   * @param {string} userType - 'student' or 'teacher'
   * @param {string} subject - Subject context
   * @param {Array} conversationHistory - Previous conversation context
   * @returns {Promise<Object>} Enhanced context for LLM prompt
   */
  async generateEnhancedContext(queryResults, userQuery, userType, subject, conversationHistory = []) {
    try {
      console.log(`[CONTEXT-INJECTION] Processing ${queryResults.length} query results`);
      
      // Step 1: Filter relevant results
      const relevantResults = this.filterRelevantResults(queryResults, userQuery);
      
      // Step 2: Summarize and structure data by collection
      const structuredContext = await this.structureContextByCollection(relevantResults, userType);
      
      // Step 3: Generate contextual insights
      const contextualInsights = await this.generateContextualInsights(structuredContext, userQuery, subject);
      
      // Step 4: Create the enhanced prompt context
      const enhancedContext = this.buildEnhancedPromptContext(
        structuredContext,
        contextualInsights,
        userQuery,
        userType,
        subject,
        conversationHistory
      );
      
      return {
        success: true,
        context: enhancedContext,
        metadata: {
          originalResultsCount: queryResults.length,
          relevantResultsCount: relevantResults.length,
          collectionsProcessed: Object.keys(structuredContext),
          contextSize: JSON.stringify(enhancedContext).length,
          generatedAt: new Date()
        }
      };
      
    } catch (error) {
      console.error('[CONTEXT-INJECTION] Error generating enhanced context:', error);
      return {
        success: false,
        error: error.message,
        fallbackContext: this.generateFallbackContext(userQuery, userType, subject)
      };
    }
  }

  /**
   * Filter query results based on relevance to user query
   */
  filterRelevantResults(queryResults, userQuery) {
    return queryResults.filter(result => {
      if (!result.success || !result.data) {
        return false;
      }
      
      // Basic relevance filtering
      const hasData = Array.isArray(result.data) ? result.data.length > 0 : result.data !== null;
      const isRelevantCollection = this.isCollectionRelevant(result.collection, userQuery);
      
      return hasData && isRelevantCollection;
    });
  }

  /**
   * Check if a collection is relevant to the user query
   */
  isCollectionRelevant(collection, userQuery) {
    const queryLower = userQuery.toLowerCase();
    const relevanceMap = {
      'students': ['profile', 'information', 'details', 'about me'],
      'studentKnowledgeGraph': ['progress', 'learning', 'curriculum', 'topics', 'proficiency'],
      'testHistory': ['test', 'exam', 'performance', 'score', 'marks'],
      'aegisGrader': ['analysis', 'detailed', 'question', 'answer', 'grading'],
      'questions': ['question', 'content', 'topic', 'practice'],
      'curriculumNodes': ['curriculum', 'syllabus', 'chapter', 'topic']
    };
    
    const keywords = relevanceMap[collection] || [];
    return keywords.some(keyword => queryLower.includes(keyword));
  }

  /**
   * Structure context data by collection type
   */
  async structureContextByCollection(relevantResults, userType) {
    const structuredContext = {};
    
    for (const result of relevantResults) {
      const collection = result.collection;
      const data = result.data;
      
      if (!structuredContext[collection]) {
        structuredContext[collection] = {
          summary: null,
          keyInsights: [],
          rawData: [],
          dataCount: 0
        };
      }
      
      // Process data based on collection type
      const processedData = await this.processCollectionData(collection, data, userType);
      structuredContext[collection] = {
        ...structuredContext[collection],
        ...processedData,
        dataCount: Array.isArray(data) ? data.length : 1
      };
    }
    
    return structuredContext;
  }

  /**
   * Process data for specific collection types
   */
  async processCollectionData(collection, data, userType) {
    switch (collection) {
      case 'students':
        return this.processStudentData(data, userType);
      
      case 'studentKnowledgeGraph':
        return this.processKnowledgeGraphData(data);
      
      case 'testHistory':
        return this.processTestHistoryData(data);
      
      case 'aegisGrader':
        return this.processGraderData(data);
      
      case 'questions':
        return this.processQuestionData(data);
      
      default:
        return {
          summary: `${collection} data available`,
          keyInsights: [],
          rawData: Array.isArray(data) ? data.slice(0, 5) : [data] // Limit raw data
        };
    }
  }

  /**
   * Process student profile data
   */
  processStudentData(data, userType) {
    const students = Array.isArray(data) ? data : [data];
    
    if (userType === 'teacher') {
      return {
        summary: `Class data for ${students.length} students`,
        keyInsights: [
          `Total students: ${students.length}`,
          `Subjects covered: ${this.extractUniqueSubjects(students).join(', ')}`
        ],
        rawData: students.map(s => ({
          name: `${s.firstName} ${s.lastName}`,
          subjects: s.subjects?.map(sub => sub.subjectName) || []
        }))
      };
    } else {
      const student = students[0];
      return {
        summary: `Student profile for ${student?.firstName} ${student?.lastName}`,
        keyInsights: [
          `Enrolled subjects: ${student?.subjects?.length || 0}`,
          `Active classes: ${student?.classes?.length || 0}`
        ],
        rawData: [{
          name: `${student?.firstName} ${student?.lastName}`,
          subjects: student?.subjects?.map(sub => sub.subjectName) || []
        }]
      };
    }
  }

  /**
   * Process knowledge graph data
   */
  processKnowledgeGraphData(data) {
    const kgData = Array.isArray(data) ? data : [data];
    const totalProgress = kgData.reduce((sum, kg) => sum + (kg.overallProficiency || 0), 0);
    const avgProficiency = kgData.length > 0 ? totalProgress / kgData.length : 0;
    
    return {
      summary: `Learning progress data for ${kgData.length} subject(s)`,
      keyInsights: [
        `Average proficiency: ${avgProficiency.toFixed(1)}%`,
        `Topics in progress: ${this.countProgressTopics(kgData)}`,
        `Completed topics: ${this.countCompletedTopics(kgData)}`
      ],
      rawData: kgData.map(kg => ({
        subject: kg.subject,
        proficiency: kg.overallProficiency,
        progressCount: kg.curriculumProgress?.length || 0
      }))
    };
  }

  /**
   * Process test history data
   */
  processTestHistoryData(data) {
    const tests = Array.isArray(data) ? data : [data];
    const recentTests = tests.slice(0, 5); // Focus on recent tests
    
    return {
      summary: `Test history with ${tests.length} test(s)`,
      keyInsights: [
        `Recent tests: ${recentTests.length}`,
        `Test types: ${this.extractUniqueTestTypes(tests).join(', ')}`,
        `Subjects tested: ${this.extractUniqueSubjects(tests).join(', ')}`
      ],
      rawData: recentTests.map(test => ({
        subject: test.subject,
        testType: test.testType,
        date: test.testDate,
        questions: test.numberOfQuestions,
        totalMarks: test.totalMarks
      }))
    };
  }

  /**
   * Process grader analysis data
   */
  processGraderData(data) {
    const graderData = Array.isArray(data) ? data : [data];
    const avgScore = graderData.reduce((sum, g) => sum + (g.overallScore || 0), 0) / graderData.length;
    
    return {
      summary: `Detailed analysis for ${graderData.length} test(s)`,
      keyInsights: [
        `Average score: ${avgScore.toFixed(1)}%`,
        `Tests analyzed: ${graderData.length}`,
        `Question-level insights available`
      ],
      rawData: graderData.map(g => ({
        testId: g.testId,
        score: g.overallScore,
        analysisDate: g.gradedAt,
        questionCount: g.questionAnalysis?.length || 0
      }))
    };
  }

  /**
   * Process question data
   */
  processQuestionData(data) {
    const questions = Array.isArray(data) ? data : [data];
    
    return {
      summary: `Question bank with ${questions.length} question(s)`,
      keyInsights: [
        `Topics covered: ${this.extractUniqueTopics(questions).join(', ')}`,
        `Difficulty range: ${this.getDifficultyRange(questions)}`,
        `Question types available`
      ],
      rawData: questions.slice(0, 3).map(q => ({ // Limit to 3 questions
        topic: q.topic,
        subtopic: q.subtopic,
        difficulty: q.difficulty,
        hasImages: q.images?.length > 0
      }))
    };
  }

  /**
   * Generate contextual insights using LLM
   */
  async generateContextualInsights(structuredContext, userQuery, subject) {
    try {
      const insightPrompt = `
Analyze the following educational data and generate 3-5 key insights relevant to the user's query.

USER QUERY: "${userQuery}"
SUBJECT: ${subject}

DATA SUMMARY:
${Object.entries(structuredContext).map(([collection, data]) =>
  `${collection}: ${data.summary}`
).join('\n')}

Generate concise, actionable insights that directly relate to the user's question. Focus on:
1. Performance patterns
2. Learning progress
3. Areas needing attention
4. Strengths and achievements
5. Recommendations

IMPORTANT: You must respond with ONLY a valid JSON array of strings. Do not include any explanatory text, markdown formatting, or other content.

Example format:
["Student shows strong progress in algebra", "Needs improvement in geometry", "Consistent test performance"]

Your response:`;

      const model = genAI.getGenerativeModel({ 
        model: contextProcessingModel,
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 512
        }
      });
      
      const result = await model.generateContent(insightPrompt);
      const response = await result.response;
      let responseText = response.text().trim();

      // Clean up the response text to extract JSON
      if (responseText.startsWith('```json')) {
        responseText = responseText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (responseText.startsWith('```')) {
        responseText = responseText.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Try to parse JSON, with multiple fallback strategies
      let insights;
      try {
        insights = JSON.parse(responseText);
      } catch (parseError) {
        console.warn('[CONTEXT-INJECTION] Failed to parse JSON, attempting to extract array:', parseError.message);

        // Strategy 1: Try to extract array from malformed JSON
        const arrayMatch = responseText.match(/\[(.*?)\]/s);
        if (arrayMatch) {
          try {
            insights = JSON.parse(arrayMatch[0]);
          } catch (secondParseError) {
            console.warn('[CONTEXT-INJECTION] Array extraction failed, trying string parsing');
            insights = null;
          }
        }

        // Strategy 2: If no array found, try to extract quoted strings
        if (!insights) {
          const quotedStrings = responseText.match(/"([^"]+)"/g);
          if (quotedStrings && quotedStrings.length > 0) {
            insights = quotedStrings.map(str => str.replace(/"/g, ''));
            console.warn('[CONTEXT-INJECTION] Extracted insights from quoted strings');
          }
        }

        // Strategy 3: If still no insights, create from natural language
        if (!insights) {
          // Split by sentences and take first few as insights
          const sentences = responseText.split(/[.!?]+/).filter(s => s.trim().length > 10);
          if (sentences.length > 0) {
            insights = sentences.slice(0, 3).map(s => s.trim());
            console.warn('[CONTEXT-INJECTION] Created insights from natural language sentences');
          } else {
            insights = ['Data analysis available for personalized response'];
            console.warn('[CONTEXT-INJECTION] Using final fallback insight');
          }
        }
      }

      return Array.isArray(insights) ? insights : [];
      
    } catch (error) {
      console.error('[CONTEXT-INJECTION] Error generating insights:', error);
      return ['Data analysis available for personalized response'];
    }
  }

  /**
   * Build the final enhanced prompt context
   */
  buildEnhancedPromptContext(structuredContext, insights, userQuery, userType, subject, conversationHistory) {
    return {
      userQuery,
      userType,
      subject,
      contextualInsights: insights,
      dataContext: structuredContext,
      conversationHistory: conversationHistory.slice(-3), // Last 3 messages
      systemContext: {
        dataRetrievalMethod: 'targeted_query_system',
        collectionsAccessed: Object.keys(structuredContext),
        contextGeneratedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Generate fallback context when main processing fails
   */
  generateFallbackContext(userQuery, userType, subject) {
    return {
      userQuery,
      userType,
      subject,
      contextualInsights: ['I can help you with your educational questions'],
      dataContext: {},
      fallbackMode: true,
      systemContext: {
        dataRetrievalMethod: 'fallback_mode',
        contextGeneratedAt: new Date().toISOString()
      }
    };
  }

  // Helper methods for data processing
  extractUniqueSubjects(data) {
    const subjects = new Set();
    data.forEach(item => {
      if (item.subject) subjects.add(item.subject);
      if (item.subjects) item.subjects.forEach(s => subjects.add(s.subjectName || s));
    });
    return Array.from(subjects);
  }

  extractUniqueTestTypes(tests) {
    return [...new Set(tests.map(t => t.testType).filter(Boolean))];
  }

  extractUniqueTopics(questions) {
    return [...new Set(questions.map(q => q.topic).filter(Boolean))];
  }

  getDifficultyRange(questions) {
    const difficulties = questions.map(q => q.difficulty).filter(d => d !== undefined);
    if (difficulties.length === 0) return 'Not specified';
    const min = Math.min(...difficulties);
    const max = Math.max(...difficulties);
    return `${min.toFixed(1)} - ${max.toFixed(1)}`;
  }

  countProgressTopics(kgData) {
    return kgData.reduce((count, kg) => {
      return count + (kg.curriculumProgress?.filter(p => p.proficiency < 80).length || 0);
    }, 0);
  }

  countCompletedTopics(kgData) {
    return kgData.reduce((count, kg) => {
      return count + (kg.curriculumProgress?.filter(p => p.proficiency >= 80).length || 0);
    }, 0);
  }

  // Initialize configuration objects
  initializeContextTemplates() {
    return {
      student: {
        greeting: "Based on your learning data",
        dataPrefix: "Your academic progress shows",
        insightPrefix: "Key insights for you"
      },
      teacher: {
        greeting: "Based on your class data",
        dataPrefix: "Your students' performance indicates",
        insightPrefix: "Class insights"
      }
    };
  }

  initializeRelevanceThresholds() {
    return {
      minDataPoints: 1,
      maxContextSize: 8000, // characters
      maxInsights: 5
    };
  }

  initializeSummarizationRules() {
    return {
      maxRawDataItems: 5,
      maxInsightLength: 100,
      prioritizeRecent: true
    };
  }
}

export default new ContextInjectionService();
