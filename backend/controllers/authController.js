import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import School from '../models/School.js';
import { validationResult } from 'express-validator';
import { genTokens } from '../services/genTokenService.js';
import { sendVerificationEmail, verifyEmail } from '../services/emailVerifyService.js';
import { OAuth2Client } from 'google-auth-library';

// Google OAuth2 client for verifying ID tokens
const googleClient = new OAuth2Client();

// Register logic
export const registerStudent = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { userType, username, firstName, lastName, email, password, schoolId, profileImage } = req.body; // <-- add profileImage

    try {
        // Check if email already exists
        const existingStudent = await Student.findOne({ email });
        if (existingStudent) {
            return res.status(400).json({ message: 'Email already registered' });
        }

        // Create student without subjects
        const student = new Student({
            role: userType,
            username,
            firstName,
            lastName,
            email: email || null,
            password,
            profileImage: profileImage || null, // <-- set profileImage
            subjects: [],  // Start with empty subjects array 
            schoolCode: schoolId,
            isEmailVerified: email? false : true // Set initial verification status to false
        });

        // Save student to get ID
        await student.save();

        if (schoolId) {
            // Add student to school
            const school = await School.findOne({ schoolCode: schoolId });
            if (!school) {
                // If school not found, delete the student we just created
                await Student.findByIdAndDelete(student._id);
                return res.status(400).json({ message: 'Invalid school' });
            }
            
            school.students.push(student._id);
            await school.save();
        }

        // Generate tokens but don't create full access yet since email isn't verified
        const [access, refresh] = genTokens(student);

        // Save the refresh token in the database with current user
        student.set({ securityToken: refresh });
        await student.save();

        if (email && !student.isEmailVerified) {
            // Get the origin from the request headers for verification link
            const origin = req.get('origin') || req.headers.referer || `http://${req.headers.host}`;

            // Send verification email
            await sendVerificationEmail(student, origin);
        }
        // Set cookie but indicate in response that verification is needed
        res.cookie('refresh', refresh, { httpOnly: true, maxAge: 24 * 60 * 60 * 1000 });

        res.status(201).json({
            message: email? 'Student registered successfully. Please check your email to verify your account.' : 'Student registered successfully without email verification.',
            accessToken: access,
            verified: email? false : true 
        });

    } catch (err) {
        console.error('Registration error:', err);
        res.status(500).json({ error: err.message });
    }
};

// Login logic
export const loginStudent = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { email, password } = req.body;
    try {
        console.error(`email: ${email}`);
        const student = await Student.findOne({
            $or: [
                { email: email },
                { username: email }
            ]
        });
        if (!student || !(await student.comparePassword(password))) {
            console.error(`user not found`);
            return res.status(400).json({ message: 'Invalid credentials' });
        }

        // Check if email is verified
        if (!student.isEmailVerified) {
            // Generate a new token for verification if needed
            const origin = req.get('origin') || req.headers.referer || `http://${req.headers.host}`;

            return res.status(401).json({
                message: 'Email not verified. Please check your inbox or request a new verification link.',
                verified: false,
                needsVerification: true
            });
        }

        const [accessToken, refreshToken] = genTokens(student);

        // save the refresh token in the database with current user
        student.set({ securityToken: refreshToken });
        await student.save();

        res.cookie('refresh', refreshToken, {
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            secure: process.env.NODE_ENV === 'production'
        });
        res.json({
            accessToken: accessToken,
            success: true,
            verified: true
        });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Register logic for Teacher
export const registerTeacher = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { userType, username, email, password, schoolId, profileImage } = req.body; // <-- add profileImage
    try {
        // Check if email already exists
        const existingTeacher = await Teacher.findOne({ email });
        if (existingTeacher) {
            return res.status(400).json({ message: 'Email already registered' });
        }

        const teacher = new Teacher({
            role: userType,
            username,
            email,
            password,
            profileImage: profileImage || "", // <-- set profileImage
            schoolCode: schoolId,
            isEmailVerified: false // Set initial verification status to false
        });
        await teacher.save();

        // Add teacher to school
        const school = await School.findOne({ schoolCode: schoolId });
        if (!school) {
            // Clean up the teacher record if school validation fails
            await Teacher.findByIdAndDelete(teacher._id);
            return res.status(400).json({ message: 'Invalid school' });
        }

        school.teachers.push(teacher._id);
        await school.save();

        const [access, refresh] = genTokens(teacher);

        // save the refresh token in the database with current user
        teacher.set({ securityToken: refresh });
        await teacher.save();

        // Get the origin from the request headers for verification link
        const origin = req.get('origin') || req.headers.referer || `http://${req.headers.host}`;

        // Send verification email
        await sendVerificationEmail(teacher, origin);

        res.cookie('refresh', refresh, {
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            secure: process.env.NODE_ENV === 'production'
        });
        res.status(201).json({
            message: 'Teacher registered successfully. Please check your email to verify your account.',
            accessToken: access,
            verified: false
        });
    } catch (err) {
        console.error('Teacher registration error:', err);
        res.status(500).json({ error: err.message });
    }
};

// Login logic for Teacher
export const loginTeacher = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { email, password } = req.body;
    try {
        const teacher = await Teacher.findOne({ $or: [{ email: email }, { username: email }] });
        if (!teacher || !(await teacher.comparePassword(password))) {
            return res.status(400).json({ message: 'Invalid credentials' });
        }

        // Check if email is verified
        if (!teacher.isEmailVerified) {
            return res.status(401).json({
                message: 'Email not verified. Please check your inbox or request a new verification link.',
                verified: false,
                needsVerification: true
            });
        }

        const [accessToken, refreshToken] = genTokens(teacher);

        // save the refresh token in the database with current user
        teacher.set({ securityToken: refreshToken });
        await teacher.save();

        res.cookie('refresh', refreshToken, {
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            secure: process.env.NODE_ENV === 'production'
        });
        res.json({
            accessToken: accessToken,
            success: true,
            verified: true
        });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Add verification routes
export const verifyUserEmail = async (req, res) => {
    try {
        const { token } = req.query;

        if (!token) {
            return res.status(400).json({ message: 'Verification token is required' });
        }

        const result = await verifyEmail(token);
        res.status(200).json(result);
    } catch (error) {
        console.error('Email verification error:', error);
        res.status(400).json({ message: error.message });
    }
};

// Resend verification email
export const resendVerificationEmail = async (req, res) => {
    try {
        const { email, userType } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        let user;
        if (userType === 'Student') {
            user = await Student.findOne({ email });
        } else if (userType === 'Teacher') {
            user = await Teacher.findOne({ email });
        } else {
            return res.status(400).json({ message: 'Invalid user type' });
        }

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        if (user.isEmailVerified) {
            return res.status(400).json({ message: 'Email already verified' });
        }

        // Get the origin for verification link
        const origin = req.get('origin') || req.headers.referer || `http://${req.headers.host}`;

        // Resend verification email
        await sendVerificationEmail(user, origin);

        res.status(200).json({ message: 'Verification email sent successfully' });
    } catch (error) {
        console.error('Resend verification error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

// Google registration/login logic
export const googleAuth = async (req, res) => {
    const { idToken, userType } = req.body;
    if (!idToken || !userType) {
        return res.status(400).json({ message: 'Missing idToken or userType' });
    }
    try {
        // DEBUG: Log the idToken and audience
        console.error('[GOOGLE AUTH DEBUG] idToken:', idToken);
        console.error('[GOOGLE AUTH DEBUG] audience:', process.env.GOOGLE_CLIENT_ID);
        // Verify Google ID token
        const ticket = await googleClient.verifyIdToken({
            idToken,
            audience: process.env.GOOGLE_CLIENT_ID // Set this in your .env
        });
        const payload = ticket.getPayload();
        console.error('[GOOGLE AUTH DEBUG] payload:', payload);
        const email = payload.email;
        const firstName = payload.given_name;
        const lastName = payload.family_name;
        const profileImage = payload.picture;
        const username = email.split('@')[0];

        let user;
        if (userType === 'Student') {
            user = await Student.findOne({ email });
            if (!user) {
                // Set a random password for Google users
                const randomPassword = Math.random().toString(36).slice(-8) + Date.now();
                user = new Student({
                    role: userType,
                    username: username || payload.name || email.split('@')[0],
                    firstName: firstName || payload.given_name || '',
                    lastName: lastName || payload.family_name || '',
                    email,
                    profileImage: profileImage || payload.picture || '',
                    subjects: [],
                    isEmailVerified: true,
                    password: randomPassword
                });
                await user.save();
            }
        } else if (userType === 'Teacher') {
            user = await Teacher.findOne({ email });
            if (!user) {
                user = new Teacher({
                    role: userType,
                    username: username || payload.name || email.split('@')[0],
                    email,
                    profileImage: profileImage || payload.picture || '',
                    isEmailVerified: true
                });
                await user.save();
            }
        } else {
            return res.status(400).json({ message: 'Invalid user type' });
        }

        // Generate tokens
        const [accessToken, refreshToken] = genTokens(user);
        user.set({ securityToken: refreshToken });
        await user.save();
        res.cookie('refresh', refreshToken, {
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            secure: process.env.NODE_ENV === 'production'
        });
        res.status(200).json({
            message: 'Login successful',
            accessToken,
            verified: true,
            email: user.email
        });
    } catch (err) {
        console.error('Google Auth error:', err);
        res.status(401).json({ message: 'Invalid Google token' });
    }
};
