import { validationResult } from "express-validator";
import { getStudentKnowledgeGraphService } from "../services/getKnowledgeGraphService.js";
// import { StudentCurriculumModel } from "../models/StudentKnowledgeGraphModel.js";
// import Question from "../models/Question.js";
import { createQuestionModel } from "../models/Question.js";
import Class from "../models/Class.js";
import { spawn } from "child_process";
import { getCollectionNameClass } from "../services/testService.js";

export const RecommendQuestions = async (req, res) => {
  try {
    // check errors in requeset
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log("[ERROR] validation error: ", errors.array());
      return res.status(401).json({message: "Invalid request", errors: errors.array()});
    }
    // fetch the knowledge graph and the questionbank
    const { classId, testType, topics, numQuestions, subject, userid } = req.body;
    console.log(`[INFO] got classId: ${classId}, testType: ${testType}, topics: ${topics}, numQuestions: ${numQuestions}, subject: ${subject}, userid: ${userid}`);

    //get student curriculum or average class curriculum
    const curriculum = await getCurriculum(classId, testType, subject, userid);
    // console.error("[INFO] curriculum: ", JSON.stringify(curriculum));
    if (!curriculum) {
      return res.status(404).json({ message: "Curriculum not found" });
    }

    // get the filtered questions
    const collectionName = await getCollectionNameClass(classId, subject);
    const filteredQuestions = await getFilteredQuestions(collectionName, topics);
    if (!filteredQuestions) {
      return res.status(404).json({ message: "Questions not found" });
    }
    // get the recommendations from python
    const data = {
      questionBank: filteredQuestions,
      knowledgeGraph: curriculum,
      numQuestions: numQuestions,
      testtype: testType,
    };
    const recommendations = await getPythonRecommendations(data);
    // console.error("[INFO] recommendations: ", JSON.stringify(recommendations));
    if (!recommendations) {
      return res.status(404).json({ message: "Recommendations not found" });
    }
    return res.status(200).json({ recommendations });
  } catch (error) {
    console.error("[ERROR] RecommendQuestions error: ", error);
    return res.status(500).json({ message: "Internal Server Error" , error: error.message});
  }

};

async function getCurriculum(classId, testType, subject, userid)  {
    if (testType !== "generic" && testType !== "diagnostic" && testType !== "personalized" && testType != "personalizedIndividual") {
      // invalid test type
      console.error("[ERROR] invalid test type: ", testType);
      return null;
    }
    const populatedClass = await Class.findOne({ _id: classId }).populate("students");
    if (!populatedClass) {
      console.error("[ERROR] populatedClass not found: ", classId);
      return null;
    }

    if (testType === "personalizedIndividual") {
      // For personalized individual test, find the specific student
      const student = populatedClass.students.find(student => student._id.toString() === userid);
      if (!student) {
        console.error("[ERROR] Student not found in class: ", userid);
        return null;
      }
      // Get the student's knowledge graph
      const curriculum = await getStudentKnowledgeGraphService(student._id, subject);
      if (!curriculum) {
        console.error("[ERROR] Curriculum not found for student: ", userid);
        return null;
      }
      const curricula = await Promise.all([curriculum]);
      const validCurricula = curricula.filter(curriculum => curriculum !== null);
      if (validCurricula.length === 0) {
        return null;
      }
      return validCurricula;
    }

    const studentIdArray = populatedClass.students.map(student => student._id);
    const curriculaPromises = studentIdArray.map(studentId =>
      getStudentKnowledgeGraphService(studentId, subject)
    );
    // Fetch all student curricula in parallel
    const curricula = await Promise.all(curriculaPromises);
    const validCurricula = curricula.filter(curriculum => curriculum !== null);
    if (validCurricula.length === 0) {
      return null;
    }
  if (testType === "generic" || testType === "diagnostic") {
    // Create an aggregated knowledge graph
    const aggregatedGraph = {
      curriculumProgress: aggregateClassProgress(validCurricula)
    };
    return aggregatedGraph;
  } else if (testType === "personalized"){
    return validCurricula;
  } else {
    return null;
  }
}

function aggregateClassProgress(curricula) {
  const nodeMap = new Map();

  // First pass: Collect all nodes and their proficiency data
  curricula.forEach(curriculum => {
    curriculum.curriculumProgress.forEach(progress => {
      const nodeId = progress.nodeId._id.toString();

      if (!nodeMap.has(nodeId)) {
        // Store complete node information
        nodeMap.set(nodeId, {
          nodeId: {
            _id: progress.nodeId._id,
            type: progress.nodeId.type,
            name: progress.nodeId.name,
            description: progress.nodeId.description,
            parents: progress.nodeId.parents,
            children: progress.nodeId.children,
            order: progress.nodeId.order,
            createdAt: progress.nodeId.createdAt,
            updatedAt: progress.nodeId.updatedAt
          },
          proficiencyScores: [], // Store all proficiency scores for statistics
          proficiencySum: 0,
          count: 0
        });
      }

      const node = nodeMap.get(nodeId);
      node.proficiencyScores.push(progress.proficiency);
      node.proficiencySum += progress.proficiency;
      node.count += 1;

    });
  });

  // Calculate statistics and format the final output
  const finalAggregatedCurriculum = Array.from(nodeMap.values()).map(node => {
    const avgProficiency = node.proficiencySum / node.count;
    const variance = node.proficiencyScores.reduce((acc, score) => {
      const diff = score - avgProficiency;
      return acc + (diff * diff);
    }, 0) / node.count;

    const finalNode = {
      nodeId: node.nodeId,
      proficiency: avgProficiency,
      statistics: {
        variance,
        standardDeviation: Math.sqrt(variance),
        numStudents: node.count,
        minProficiency: Math.min(...node.proficiencyScores),
        maxProficiency: Math.max(...node.proficiencyScores)
      }
    };

    return finalNode;
  }).sort((a, b) => (a.nodeId.order || 0) - (b.nodeId.order || 0));

  // console.error("Final sorted result:", finalAggregatedCurriculum);
  return finalAggregatedCurriculum;

}

function analyzeCurriculum(aggregatedProgress) {
  // Group nodes by type
  const groupedByType = aggregatedProgress.reduce((acc, node) => {
    const type = node.nodeId.type;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(node);
    return acc;
  }, {});

  // Sort nodes within each type by order
  Object.keys(groupedByType).forEach(type => {
    groupedByType[type].sort((a, b) => a.nodeId.order - b.nodeId.order);
  });

  // Create summary for each type
  const summary = {};
  Object.entries(groupedByType).forEach(([type, nodes]) => {
    summary[type] = {
      count: nodes.length,
      averageProficiency: calculateAverage(nodes.map(n => n.proficiency)),
      nodes: nodes.map(node => ({
        name: node.nodeId.name,
        proficiency: node.proficiency.toFixed(2),
        stats: {
          variance: node.statistics.variance.toFixed(2),
          stdDev: node.statistics.standardDeviation.toFixed(2),
          students: node.statistics.numStudents,
          range: {
            min: node.statistics.minProficiency.toFixed(2),
            max: node.statistics.maxProficiency.toFixed(2)
          }
        }
      }))
    };
  });

  return summary;
}

function calculateAverage(numbers) {
  return (numbers.reduce((a, b) => a + b, 0) / numbers.length).toFixed(2);
}

async function getFilteredQuestions(collectionName, topics) {
  // const questionBank = await Question.find(); // find all questions
  const questionBank = await createQuestionModel(collectionName).find(); // find all the questions in the collection
  console.error("[INFO] questionBank length: ", questionBank.length);

  if (topics.length === 0 || topics === undefined) {
    return questionBank;
  }

  let filteredQuestions = [];
  if (topics) {
    filteredQuestions = questionBank.filter((obj) =>
      topics.some((topicSent) => obj.topic.split(",").includes(topicSent))
    );
  }
  return filteredQuestions;
}

function getPythonRecommendations(data) {
  const pythonPath = process.env.NODE_ENV === "production" ? "/home/<USER>/app/python-backend/recommend.py" : "./python-backend/recommend.py";
  return new Promise((resolve, reject) => {
    // call the python program
    console.error(`[INFO] starting the python process cwd: ${process.cwd()}`);
    const pythonProcess = spawn("python3", [
      pythonPath,
    ], {shell: true});

    pythonProcess.stdin.write(JSON.stringify(data));
    pythonProcess.stdin.end();

    let listOfQuestions = "";
    let errorOutput = "";

    pythonProcess.stdout.on("data", (data) => {
      // return the list of questions
      listOfQuestions += data.toString();
    });

    pythonProcess.stderr.on("data", (data) => {
      errorOutput += data.toString();
      console.error(`[PYTHON][ERROR] stderr: ${data.toString()}`);
    });

    pythonProcess.on("error", (error) => {
      console.error(`[PYTHON][ERROR][CRASH] error: ${error.message}`);
      reject(new Error("failed to start python process: " + error.message));
    });

    pythonProcess.on("close", (code) => {
      console.error(`process exited with code: ${code}`);
      if (code !== 0) {
        reject(new Error(`Python process exited with code ${code}: ${errorOutput}`));
      } else {
        resolve(listOfQuestions);
      }
    });
  });
}
