import { validationResult } from "express-validator";
import { spawn } from "child_process";
import fs from "fs";
import { parseStringPromise } from "xml2js";
import AegisGrader from "../models/AegisGrader.js";

export const getAllSubmissions = async (req, res) => {
    try {
        const submissions = await AegisGrader.find().sort({ timestamp: -1 });
        if (!submissions || submissions.length === 0) {
            return res.status(404).json({ message: "No submissions found" });
        }
        return res.status(200).json({
            message: "Submissions retrieved successfully",
            submissions: submissions.map(submission => ({
                id: submission._id,
                testDetails: submission.testDetails,
                answerSheets: submission.answerSheets,
                questionPaper: submission.questionPaper,
                rubric: submission.rubric,
                createdAt: submission.createdAt
            }))
        });
    } catch (error) {
        console.error("Error retrieving submissions:", error);
        return res.status(500).json({
            message: "Internal Server Error",
            error: error.message || "Unknown error occurred"
        });
    }
};

export const submitForGrading = async (req, res) => {
    const tempFilePath = "./aegis_grader/tempFile.json";
    const scriptPath = "aegis_grader/main.py";

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(401).json({ errors: errors.array() });
        }
        const { testDetails, answerSheets, questionPaper, rubric } = req.body;

        if (!answerSheets || answerSheets.length === 0) {
            return res.status(400).json({ message: "No answer sheets provided" });
        }
        if (!testDetails || !testDetails.className || !testDetails.subject) {
            return res.status(400).json({ message: "Invalid test details" });
        }

        try {
            fs.writeFileSync(tempFilePath, JSON.stringify({ testDetails, answerSheets, questionPaper, rubric }));
            console.error("Temp input file written successfully:", tempFilePath);
        } catch (writeError) {
            console.error("Error writing temp input file:", writeError);
            return res.status(500).json({ message: "Error writing temporary input file", error: writeError.message });
        }

        console.error("Executing Python script:", scriptPath);
        const rawOutputString = await runPythonScript(scriptPath);

        const parsedEvaluations = [];
        const evalMatches = rawOutputString.match(/<evaluation>[\s\S]*?<\/evaluation>/g);

        if (evalMatches && evalMatches.length > 0) {
            console.error(`Found ${evalMatches.length} evaluation block(s) using regex.`);
            for (let i = 0; i < evalMatches.length; i++) {
                const evalXmlString = evalMatches[i];
                console.error(`Attempting to parse evaluation block ${i + 1}...`);
                try {
                    const parsedXml = await parseStringPromise(evalXmlString, {
                        explicitArray: false,
                        tagNameProcessors: [],
                        attrNameProcessors: [],
                        valueProcessors: [],
                        attrValueProcessors: [],
                        charkey: "_",
                        trim: true,
                        normalizeTags: true,
                        normalize: true,
                        mergeAttrs: true
                    });
                    parsedEvaluations.push(parsedXml);
                    console.error(`Successfully parsed evaluation block ${i + 1}.`);
                } catch (xmlError) {
                    console.error(`Error parsing XML block ${i + 1}:`, xmlError.message);
                    console.error("Malformed XML block content:\n", evalXmlString);
                }
            }
        } else {
            console.warn("No complete <evaluation>...</evaluation> tags found in the Python script output.");
        }

        console.error(`Returning ${parsedEvaluations.length} parsed evaluations.`);
        for(let i=0; i<parsedEvaluations.length; i++){
            const newAegisGrader = new AegisGrader({
                testDetails: {
                    className: testDetails.className,
                    subject: testDetails.subject,
                    date: testDetails.date || new Date().toISOString()
                },
                answerSheets: answerSheets.map(sheet => ({
                    id: sheet.id,
                    studentName: sheet.studentName,
                    rollNumber: sheet.rollNumber,
                    pdfUrl: sheet.pdfUrl,
                    timestamp: sheet.timestamp,
                    className: testDetails.className,
                    evaluationResult: parsedEvaluations[i] || {}
                })),
                questionPaper: {
                    type: questionPaper.type || "questionPaper",
                    pdfUrl: questionPaper.pdfUrl || "",
                    timestamp: questionPaper.timestamp || Date.now()
                },
                rubric: {
                    type: rubric.type || "rubric",
                    pdfUrl: rubric.pdfUrl || "",
                    timestamp: rubric.timestamp || Date.now()
                }
            });
            await newAegisGrader.save();
            console.error(`Aegis Grader submission saved successfully for answer sheet ${i} :`, newAegisGrader);
        }
        
        return res.status(200).json({
            message: "Aegis Grader executed successfully",
            evaluations: parsedEvaluations
        });
    } catch (error) {
        console.error("Error during AegisGrader submission process:", error);
        return res.status(500).json({
            message: "Internal Server Error during grading",
            error: error.message || "Unknown error occurred"
        });
    } finally {
        fs.unlink(tempFilePath, (err) => {
            if (err) {
                console.error("Error deleting temp input file:", err);
            } else {
                console.error("Temp input file deleted successfully:", tempFilePath);
            }
        });
    }
};

async function runPythonScript(scriptPath) {
    return new Promise((resolve, reject) => {
        const pythonProcess = spawn("python3", [scriptPath]);
        let output = "";
        let errorOutput = "";

        pythonProcess.stdout.on("data", (data) => {
            output += data.toString();
        });
        pythonProcess.stderr.on("data", (data) => {
            errorOutput += data.toString();
            console.error(`Python stderr: ${data}`);
        });
        pythonProcess.on("error", (error) => {
            console.error(`Failed to start Python process: ${error.message}`);
            reject(new Error(`Failed to start Python process: ${error.message}`));
        });
        pythonProcess.on("close", (code) => {
            console.error(`Python process exited with code ${code}`);
            if (code !== 0) {
                reject(new Error(`Python script exited with code ${code}. Stderr: ${errorOutput}`));
            } else {
                resolve(output);
            }
        });
    });
}