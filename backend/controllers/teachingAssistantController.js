import { teachingAssistantService } from '../services/teachingAssistantService.js';
import enhancedTeachingAssistantService from '../services/enhancedTeachingAssistantService.js';

export const getChatbotResponse = teachingAssistantService.getChatbotResponse;
export const getChatbotResponseWithMCP = teachingAssistantService.getChatbotResponseWithMCP;

// New enhanced endpoint using text-to-MongoDB query system
export const getChatbotResponseWithQuerySystem = enhancedTeachingAssistantService.getChatbotResponseWithQuerySystem.bind(enhancedTeachingAssistantService);

// Health check endpoint for the new system
export const healthCheck = enhancedTeachingAssistantService.healthCheck.bind(enhancedTeachingAssistantService);