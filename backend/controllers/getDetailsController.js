import { model } from "mongoose";
import Student from "../models/Student.js";
import Teacher from "../models/Teacher.js";
import School from "../models/School.js";
import { validationResult } from "express-validator";

function validateRequest(req, res) {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    console.error(`[ERROR]: ${JSON.stringify(errors)}`);
    res.status(400).json({ errors: errors.array() });
    return false;
  }
  return true;
}

export async function getStudentDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const email = req.body.email;
    console.error(`[DEBUG] getStudentDetails called with email: ${email}`);

    const student = await Student.findOne({$or: [{email: email}, {username: email}]})
      .populate({
        path: "classes",
        populate: {
          path: "teacherId",
          model: "Teacher",
          select: "username email"
        }
      })
      .populate({
        path: "subjects",
        populate: {
          path: "testHistory",
          model: "TestHistory",
        }
      });

    if (!student) {
      console.error(`[DEBUG] Student not found for email: ${email}`);
      return res.status(400).json({ message: "Invalid student" });
    }

    console.error(`[DEBUG] Student found: ${student.username} (${student.email})`);

    // found the required student
    const data = {
      id: student._id,
      username: student.username,
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email,
      classId: student.classId,
      schoolCode: student.schoolCode,
      profileImage: student.profileImage, // <-- Add this line
      subjects: student.subjects, // Now includes populated test history
      role: "Student",
      tasks: student.tasks,
      classes: student.classes,
    };

    return res.status(201).json(data);
  } catch (error) {
    return res.status(400).json({ errors: error });
  }
}

export async function getTeacherDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const email = req.body.email;

    // Populate classes when querying the teacher
    const teacher = await Teacher.findOne({$or: [{ email }, {username: email}]})
      .populate([{
        path: "classes",
        populate: {
          path: "students",
          model: "Student",
          populate: {
            path: "subjects",
            populate: {
              path: "testHistory",
              model: "TestHistory"
            }
          }
        } }, {
        path: "testHistory",
      }]);

    if (!teacher) {
      return res.status(400).json({ message: "Invalid teacher email" });
    }
    const data = {
      id: teacher._id,
      username: teacher.username,
      email: teacher.email,
      profileImage: teacher.profileImage, // <-- Add this line
      subjects: teacher.subjects,
      schoolCode: teacher.schoolCode,
      role: teacher.role,
      tasks: teacher.tasks,
      classes: teacher.classes, // Now classes are already populated
      testHistory: teacher.testHistory,
    };

    return res.status(200).json(data); // Changed to 200 since it's a successful GET
  } catch (error) {
    return res.status(400).json({ error: JSON.stringify(error.message) }); // Better error handling
  }
}

export async function editStudentDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const o_uname = req.body.o_uname;
    const newemail = req.body.new_email;
    const newuname = req.body.new_uname;

    const newdoc = await Student.findOneAndUpdate(
      { username: o_uname },
      { email: newemail, username: newuname },
      { new: true }
    );
  } catch (e) {
    console.error(`[ERROR] Failed to edit student details: ${e}`);
    return res.status(400).json({ errors: e });
  }
}

export async function editTeacherDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const o_uname = req.body.o_uname;
    const newemail = req.body.new_email;
    const newuname = req.body.new_uname;

    const newdoc = await Teacher.findOneAndUpdate(
      { username: o_uname },
      { email: newemail, username: newuname },
      { new: true }
    );
  } catch (e) {
    return res.status(400).json({ errors: e });
  }
}

export async function getSchoolDetailsByCode(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const schoolCode = req.params.schoolCode;

    const schoolInfo = await School.findOne({ schoolCode: schoolCode })

    return res.status(200).json({
      schoolCode: schoolInfo.schoolCode,
      name: schoolInfo.name,
      pincode: schoolInfo.pincode,
      phone: schoolInfo.phone
    });
  }
  catch (error) {
    return res.status(400).json({ errors: error });
  }
}