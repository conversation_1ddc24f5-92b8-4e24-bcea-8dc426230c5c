import { validationResult } from "express-validator";
// import Student from '../models/Student.js';
// import Teacher from '../models/Teacher.js';
import Feedback from "../models/Feedback.js";
import multer from "multer";

// Multer configuration
const multerConfig = {
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp'];
        if (!allowedTypes.includes(file.mimetype)) {
            cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'));
            return;
        }
        cb(null, true);
    }
};

export const feedbackController = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.error("[ERROR] Validation errors:", errors.array());
    return res.status(400).json({errors: errors.array()});
  }

  try {
    const upload = multer(multerConfig).array('images', 3);
    await new Promise((resolve, reject) => {
        upload(req, res, (err) => err ? reject(err) : resolve());
    });

    if (!req.files || req.files?.length === 0) {
      return res.status(400).json({ message: 'No image files uploaded.' });
    }
    const { comment, type, id, role } = req.body;
    console.log(`[INFO] got body: ${JSON.stringify(req.body)}`);

    // Simulate saving feedback

    const imagesToStore = []
    req.files.forEach(file => {
      if (file.size > 5*1024*1024) {
        console.warn(`file: ${file.originalname} skipped due to size greater than 5MB`);
        return;
      }

      imagesToStore.push({
        filename : file.originalname,
        imageData: file.buffer,
        contentType: file.mimetype
      })
    });

    if (imagesToStore.length === 0) {
      return res.status(400).json({message: "No valid image found to upload\n"});
    }

    const newFeedback = new Feedback({
      userId: id,
      role: role, // 'student' or 'teacher'
      feedbackType: type, // 'like', 'dislike', or 'report'
      comment: comment, // Optional comment field
      images: imagesToStore
    });

    await newFeedback.save();

    return res.status(200).json({ message: 'Feedback submitted successfully' });
  } catch (error) {
    console.error('[ERROR] processing feedback:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

export const loadFeed = async (req, res) => {
  try {
    const allFeedBacks = await Feedback.find().sort({ createdAt: -1 }).limit(100);
    // console.error(`[INFO] Loaded ${allFeedBacks.length} feedback entries with latest one being: ${allFeedBacks.length > 0 ? allFeedBacks[0]: "no feedback found"}`);
    
    const processedFeedbacks = []

    allFeedBacks.forEach(async feedback => {
      // Ensure 'images' array exists and is iterable
      const processedImages = []
      if (feedback.images && Array.isArray(feedback.images)) {
         feedback.images.forEach(img => {
            // Check if imageData exists and is a Buffer before toString('base64')
            const imageDataBase64 = img.imageData instanceof Buffer
              ? `data:${img.contentType};base64,${img.imageData.toString('base64')}`
              : null; // Or handle error/missing data appropriately

            // Correctly return an object from the inner map
            processedImages.push({
              filename: img.filename,
              contentType: img.contentType,
              imageData: imageDataBase64 // This will be the Data URL string
            });
          })
      }


      const pfeedback = {
        userId: feedback.userId,
        role: feedback.role,
        feedbackType: feedback.feedbackType,
        comment : feedback.comment,
        createdAt : feedback.createdAt,
        images : processedImages,
      }

      processedFeedbacks.push(pfeedback);
    });

    console.log(`[INFO] got processed feedback: ${processedFeedbacks[0].userId}, ${processedFeedbacks[0].role}`);

    return res.status(200).json(processedFeedbacks);
  } catch (error) {
    return res.status(500).json({ message: `Internal server error: ${error.message}` });
  }
}
