import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import textToQueryService from '../services/textToQueryService.js';
import queryExecutionEngine from '../services/queryExecutionEngine.js';
import contextInjectionService from '../services/contextInjectionService.js';
import privacySecurityService from '../services/privacySecurityService.js';
import queryCacheService from '../services/queryCacheService.js';
import enhancedTeachingAssistantService from '../services/enhancedTeachingAssistantService.js';

/**
 * Comprehensive test suite for the Text-to-MongoDB Query System
 * Tests accuracy, privacy compliance, and performance
 */
describe('Text-to-MongoDB Query System', () => {
  
  describe('Text-to-Query Service', () => {
    it('should generate valid MongoDB queries from natural language', async () => {
      const result = await textToQueryService.generateQuery(
        "How am I performing in Mathematics?",
        'student',
        'test_student_123',
        'Mathematics'
      );

      expect(result.success).toBe(true);
      expect(result.queries).toBeDefined();
      expect(Array.isArray(result.queries)).toBe(true);
      expect(result.intent).toBeDefined();
      expect(result.targetCollections).toBeDefined();
    });

    it('should classify query intents correctly', async () => {
      const performanceQuery = await textToQueryService.generateQuery(
        "What are my test scores?",
        'student',
        'test_student_123',
        'Mathematics'
      );

      expect(performanceQuery.intent).toMatch(/PERFORMANCE|ANALYSIS/);

      const progressQuery = await textToQueryService.generateQuery(
        "What topics have I completed?",
        'student',
        'test_student_123',
        'Mathematics'
      );

      expect(progressQuery.intent).toMatch(/PROGRESS|LEARNING/);
    });

    it('should limit collections for different analysis modes', async () => {
      const normalResult = await textToQueryService.generateQuery(
        "Show my progress",
        'student',
        'test_student_123',
        'Mathematics',
        'normal'
      );

      const deepResult = await textToQueryService.generateQuery(
        "Show my progress",
        'student',
        'test_student_123',
        'Mathematics',
        'deep'
      );

      expect(normalResult.targetCollections.length).toBeLessThanOrEqual(2);
      expect(deepResult.targetCollections.length).toBeGreaterThan(normalResult.targetCollections.length);
    });

    it('should handle teacher vs student queries differently', async () => {
      const studentResult = await textToQueryService.generateQuery(
        "How is my class performing?",
        'student',
        'test_student_123',
        'Mathematics'
      );

      const teacherResult = await textToQueryService.generateQuery(
        "How is my class performing?",
        'teacher',
        'test_teacher_456',
        'Mathematics'
      );

      expect(studentResult.targetCollections).not.toContain('chatConversations');
      expect(teacherResult.targetCollections).not.toContain('chatConversations');
    });
  });

  describe('Privacy and Security Service', () => {
    it('should block teacher access to chat conversations', () => {
      expect(() => {
        privacySecurityService.validateCollectionAccess('chatConversations', 'teacher');
      }).toThrow(/Teachers cannot access student chat conversations/);
    });

    it('should allow student access to their own data', () => {
      expect(() => {
        privacySecurityService.validateCollectionAccess('chatConversations', 'student');
      }).not.toThrow();
    });

    it('should sanitize sensitive fields from results', () => {
      const mockStudentData = {
        _id: 'objectid123',
        username: 'student123',
        password: 'secret123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        securityToken: 'token123'
      };

      const sanitized = privacySecurityService.sanitizeQueryResults(
        [mockStudentData],
        'students',
        'student'
      );

      expect(sanitized[0].password).toBeUndefined();
      expect(sanitized[0].securityToken).toBeUndefined();
      expect(sanitized[0]._id).toBeUndefined();
      expect(sanitized[0].firstName).toBeDefined();
      expect(sanitized[0].username).toBeDefined();
    });

    it('should anonymize ObjectIds', () => {
      const mockData = {
        testId: '507f1f77bcf86cd799439011',
        studentId: '507f1f77bcf86cd799439012',
        name: 'Test Name'
      };

      const sanitized = privacySecurityService.sanitizeQueryResults(
        [mockData],
        'testHistory',
        'student'
      );

      expect(sanitized[0].testId).not.toBe('507f1f77bcf86cd799439011');
      expect(sanitized[0].studentId).not.toBe('507f1f77bcf86cd799439012');
      expect(sanitized[0].name).toBe('Test Name');
    });

    it('should validate query structure for security', () => {
      const dangerousQuery = {
        collection: 'students',
        query: { $where: 'this.password === "admin"' }
      };

      expect(() => {
        privacySecurityService.validateAndSanitizeQuery(
          dangerousQuery,
          'student',
          'test_user',
          'students'
        );
      }).toThrow(/Dangerous query operator not allowed/);
    });

    it('should apply data minimization limits', () => {
      const largeQuery = {
        collection: 'students',
        query: {},
        limit: 1000
      };

      const sanitized = privacySecurityService.validateAndSanitizeQuery(
        largeQuery,
        'student',
        'test_user',
        'students'
      );

      expect(sanitized.limit).toBeLessThanOrEqual(100);
    });
  });

  describe('Query Cache Service', () => {
    beforeEach(() => {
      // Reset cache metrics
      queryCacheService.performanceMetrics = {
        hits: 0,
        misses: 0,
        totalQueries: 0,
        avgResponseTime: 0
      };
    });

    it('should generate consistent cache keys for identical queries', () => {
      const queries = [
        { collection: 'students', operation: 'find', query: { username: 'test' } }
      ];

      const key1 = queryCacheService.generateQueryCacheKey(
        'How am I doing?',
        'user123',
        'Math',
        queries
      );

      const key2 = queryCacheService.generateQueryCacheKey(
        'How am I doing?',
        'user123',
        'Math',
        queries
      );

      expect(key1).toBe(key2);
    });

    it('should generate different cache keys for different queries', () => {
      const queries1 = [
        { collection: 'students', operation: 'find', query: { username: 'test' } }
      ];

      const queries2 = [
        { collection: 'testHistory', operation: 'find', query: { subject: 'Math' } }
      ];

      const key1 = queryCacheService.generateQueryCacheKey(
        'How am I doing?',
        'user123',
        'Math',
        queries1
      );

      const key2 = queryCacheService.generateQueryCacheKey(
        'How am I doing?',
        'user123',
        'Math',
        queries2
      );

      expect(key1).not.toBe(key2);
    });

    it('should track cache performance metrics', () => {
      queryCacheService.performanceMetrics.hits = 5;
      queryCacheService.performanceMetrics.misses = 3;
      queryCacheService.performanceMetrics.totalQueries = 8;

      const stats = queryCacheService.getCacheStats();

      expect(stats.hitRate).toBe('62.50%');
      expect(stats.missRate).toBe('37.50%');
    });
  });

  describe('Context Injection Service', () => {
    it('should process query results into structured context', async () => {
      const mockQueryResults = [
        {
          collection: 'students',
          success: true,
          data: [{
            firstName: 'John',
            lastName: 'Doe',
            subjects: [{ subjectName: 'Mathematics' }]
          }]
        },
        {
          collection: 'testHistory',
          success: true,
          data: [{
            subject: 'Mathematics',
            testType: 'Unit Test',
            totalMarks: 85,
            testDate: new Date()
          }]
        }
      ];

      const result = await contextInjectionService.generateEnhancedContext(
        mockQueryResults,
        'How am I performing in Mathematics?',
        'student',
        'Mathematics'
      );

      expect(result.success).toBe(true);
      expect(result.context.dataContext).toBeDefined();
      expect(result.context.contextualInsights).toBeDefined();
      expect(result.context.userQuery).toBe('How am I performing in Mathematics?');
    });

    it('should filter relevant results based on query', () => {
      const queryResults = [
        {
          collection: 'students',
          success: true,
          data: [{ name: 'John' }]
        },
        {
          collection: 'irrelevantCollection',
          success: true,
          data: [{ data: 'irrelevant' }]
        }
      ];

      const relevantResults = contextInjectionService.filterRelevantResults(
        queryResults,
        'Show my profile information'
      );

      expect(relevantResults.length).toBe(1);
      expect(relevantResults[0].collection).toBe('students');
    });

    it('should generate fallback context when processing fails', async () => {
      const result = await contextInjectionService.generateEnhancedContext(
        [], // Empty results
        'Test query',
        'student',
        'Mathematics'
      );

      expect(result.success).toBe(true);
      expect(result.context.fallbackMode).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should complete full pipeline from query to response', async () => {
      // Mock request object
      const mockReq = {
        body: {
          studentId: 'test_student_123',
          subject: 'Mathematics',
          message: 'How am I performing in Mathematics?',
          conversationHistory: [],
          userName: 'John'
        }
      };

      // Mock response object
      const mockRes = {
        json: jest.fn(),
        status: jest.fn().mockReturnThis()
      };

      // This would require mocking the database and external services
      // For now, just test that the method exists and can be called
      expect(enhancedTeachingAssistantService.getChatbotResponseWithQuerySystem).toBeDefined();
      expect(typeof enhancedTeachingAssistantService.getChatbotResponseWithQuerySystem).toBe('function');
    });

    it('should handle errors gracefully and fallback to traditional method', async () => {
      // Test error handling by passing invalid data
      const mockReq = {
        body: {
          // Missing required fields
          message: 'Test query'
        }
      };

      const mockRes = {
        json: jest.fn(),
        status: jest.fn().mockReturnThis()
      };

      await enhancedTeachingAssistantService.getChatbotResponseWithQuerySystem(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe('Performance Tests', () => {
    it('should complete query generation within acceptable time', async () => {
      const startTime = Date.now();

      await textToQueryService.generateQuery(
        'Show my recent test performance',
        'student',
        'test_user',
        'Mathematics'
      );

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests', async () => {
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        promises.push(
          textToQueryService.generateQuery(
            `Query ${i}`,
            'student',
            `user_${i}`,
            'Mathematics'
          )
        );
      }

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Health Check', () => {
    it('should return system health status', async () => {
      const health = await enhancedTeachingAssistantService.healthCheck();
      
      expect(health.status).toBeDefined();
      expect(health.services).toBeDefined();
      expect(health.timestamp).toBeDefined();
    });
  });
});

// Test utilities
export const testUtils = {
  createMockStudent: () => ({
    username: 'test_student',
    firstName: 'Test',
    lastName: 'Student',
    subjects: [{ subjectName: 'Mathematics' }]
  }),

  createMockTestHistory: () => ({
    subject: 'Mathematics',
    testType: 'Unit Test',
    totalMarks: 85,
    testDate: new Date(),
    numberOfQuestions: 20
  }),

  createMockQueryResult: (collection, data) => ({
    collection,
    success: true,
    data: Array.isArray(data) ? data : [data],
    documentCount: Array.isArray(data) ? data.length : 1
  })
};
