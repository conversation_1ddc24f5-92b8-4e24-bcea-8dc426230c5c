# Text-to-MongoDB Query System Architecture

## Overview

The Text-to-MongoDB Query System is a sophisticated pipeline that converts natural language queries from students and teachers into targeted MongoDB queries, executes them securely, and injects the results as context into AegisAI responses. This system replaces the current approach of sending complete user data with every chat message, optimizing performance and maintaining privacy.

## System Architecture

### Core Components

1. **Query Generation LLM Service** (`textToQueryService.js`)
   - Converts natural language to structured MongoDB queries
   - Understands collection schemas and relationships
   - Applies privacy and access control rules

2. **Query Execution Engine** (`queryExecutionEngine.js`)
   - Securely executes generated MongoDB queries
   - Sanitizes results and removes sensitive data
   - Enforces teacher-student access controls

3. **Context Injection System** (`contextInjectionService.js`)
   - Processes query results into LLM-ready context
   - Summarizes and filters relevant information
   - Maintains privacy guidelines

4. **Analysis Mode Controller** (`analysisModeController.js`)
   - Manages Normal vs Deep Analysis modes
   - Enforces subscription-based usage limits
   - Tracks usage and rate limiting

## Data Flow Pipeline

```mermaid
graph TD
    A[User Natural Language Query] --> B[Query Classification]
    B --> C[Query Generation LLM]
    C --> D[Query Validation & Security Check]
    D --> E[Query Execution Engine]
    E --> F[Result Sanitization]
    F --> G[Context Injection System]
    G --> H[Main LLM with Injected Context]
    H --> I[Final Response to User]
    
    J[Analysis Mode Detection] --> C
    K[User Subscription Check] --> J
    L[Rate Limiting Check] --> J
    
    M[Cache Check] --> C
    N[Cache Store] --> G
```

## Collection Targeting Strategy

### Primary Collections
1. **students** - Basic student profile and academic data
2. **studentKnowledgeGraph** - Learning progress and curriculum tracking
3. **testHistory** - Test performance and analytics
4. **aegisGrader** - Detailed test analysis and grading
5. **Dynamic Question Collections** - Subject-specific question banks

### Query Classification
- **Profile Queries**: Target `students` collection
- **Progress Queries**: Target `studentKnowledgeGraph` collection
- **Performance Queries**: Target `testHistory` and `aegisGrader` collections
- **Content Queries**: Target dynamic question collections
- **Curriculum Queries**: Target curriculum node collections

## Analysis Modes

### Normal Analysis Mode
- **Scope**: Shallow context, basic information
- **Collections**: Limited to 2-3 collections per query
- **Limits**: No usage restrictions
- **Query Complexity**: Simple aggregations only
- **Cache TTL**: 24 hours

### Deep Analysis Mode
- **Scope**: Comprehensive context, detailed insights
- **Collections**: Access to all relevant collections
- **Limits**: Subscription-based (Basic: 1/month, Premium: 3/day)
- **Query Complexity**: Complex aggregations and joins
- **Cache TTL**: 7 days

## Privacy and Security Framework

### Access Control Matrix
| User Type | students | knowledgeGraph | testHistory | aegisGrader | chatConversations |
|-----------|----------|----------------|-------------|-------------|-------------------|
| Student   | Own data | Own data       | Own data    | Own data    | Own data          |
| Teacher   | Class data| Class data     | Class data  | Class data  | ❌ BLOCKED        |

### Data Sanitization Rules
1. **Remove Raw ObjectIds**: Replace with meaningful references
2. **Filter Sensitive Data**: Remove personal identifiers, device info
3. **Resolve References**: Convert nodeIds to topic names
4. **Aggregate Personal Data**: Anonymize individual student data for teachers

## Integration Points

### Existing System Integration
- **AegisAI Chat**: Replace `mongoMcpService.generateEnhancedPrompt()`
- **Cache System**: Integrate with existing `LLMCache` model
- **Rate Limiting**: Extend existing `LLMRateLimit` model
- **Privacy Controls**: Maintain existing teacher access restrictions

### API Endpoints
- `POST /api/teaching-assistant/chat-with-query` - New enhanced endpoint
- `POST /api/query-generation/analyze` - Query generation service
- `GET /api/analysis-mode/usage` - Usage tracking endpoint

## Performance Optimization

### Caching Strategy
1. **Query Cache**: Cache generated MongoDB queries (1 hour TTL)
2. **Result Cache**: Cache query results (varies by analysis mode)
3. **Context Cache**: Cache processed context (24 hours TTL)

### Query Optimization
- Index-aware query generation
- Projection-based field selection
- Aggregation pipeline optimization
- Connection pooling for database access

## Implementation Phases

### Phase 1: Core Infrastructure
- Query Generation LLM Service
- Query Execution Engine
- Basic privacy controls

### Phase 2: Analysis Modes
- Normal/Deep analysis implementation
- Usage tracking and rate limiting
- Subscription integration

### Phase 3: Integration & Optimization
- AegisAI chat integration
- Performance optimization
- Comprehensive testing

## Error Handling

### Query Generation Errors
- Fallback to traditional mongoMcpService
- Log failed query attempts for improvement
- Provide generic educational responses

### Execution Errors
- Graceful degradation to cached data
- Privacy-safe error messages
- Automatic retry mechanisms

### Rate Limiting
- Clear usage limit notifications
- Upgrade prompts for Basic users
- Graceful fallback to Normal Analysis

## Monitoring and Analytics

### Key Metrics
- Query generation success rate
- Query execution performance
- Cache hit rates
- Analysis mode usage patterns
- Privacy compliance violations

### Alerting
- Failed privacy checks
- Unusual query patterns
- Performance degradation
- Rate limit violations

## Future Enhancements

### Advanced Features
- Natural language query suggestions
- Query result explanations
- Personalized query optimization
- Multi-language query support

### AI Improvements
- Query intent classification
- Result relevance scoring
- Adaptive context sizing
- Predictive query caching
