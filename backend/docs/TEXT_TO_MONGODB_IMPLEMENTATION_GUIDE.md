# Text-to-MongoDB Query System - Implementation Guide

## Overview

The Text-to-MongoDB Query System is a sophisticated pipeline that converts natural language queries into targeted MongoDB operations, optimizing data retrieval for AegisAI while maintaining strict privacy controls and performance standards.

## System Architecture

### Core Components

1. **Text-to-Query Service** (`textToQueryService.js`)
   - Converts natural language to MongoDB queries using Gemini LLM
   - Classifies query intent and determines target collections
   - Applies privacy controls and access restrictions

2. **Query Execution Engine** (`queryExecutionEngine.js`)
   - Securely executes MongoDB queries with privacy validation
   - Enforces teacher-student access controls
   - Sanitizes results and removes sensitive data

3. **Context Injection Service** (`contextInjectionService.js`)
   - Processes query results into structured context
   - Generates contextual insights using LLM analysis
   - Builds enhanced prompts for main LLM response

4. **Privacy Security Service** (`privacySecurityService.js`)
   - Ensures DPDP Act 2023 compliance
   - Validates query structure and prevents dangerous operations
   - Anonymizes ObjectIds and removes sensitive fields

5. **Query Cache Service** (`queryCacheService.js`)
   - Implements intelligent caching with 7-day TTL
   - Optimizes performance through query result caching
   - Provides cache analytics and optimization

6. **Enhanced Teaching Assistant Service** (`enhancedTeachingAssistantService.js`)
   - Orchestrates the complete pipeline
   - Integrates with existing AegisAI chat system
   - Provides fallback to traditional method

## API Endpoints

### New Endpoints

#### POST `/api/teaching-assistant/chat-query`
Enhanced chatbot endpoint using the new query system.

**Request Body:**
```json
{
  "studentId": "string",
  "subject": "string", 
  "message": "string",
  "conversationHistory": "array",
  "conversationId": "string",
  "userName": "string"
}
```

**Response:**
```json
{
  "message": "string",
  "studentContext": {
    "knowledgeLevel": "string",
    "dataSource": "query_based_retrieval",
    "queriesExecuted": "number",
    "collectionsAccessed": "array",
    "documentsRetrieved": "number",
    "responseTime": "number",
    "cached": "boolean"
  }
}
```

#### GET `/api/teaching-assistant/health`
Health check endpoint for the query system.

**Response:**
```json
{
  "status": "healthy|unhealthy",
  "services": {
    "textToQuery": "boolean",
    "queryExecution": "boolean", 
    "contextInjection": "boolean",
    "geminiAPI": "boolean"
  },
  "timestamp": "string"
}
```

## Data Flow Pipeline

```mermaid
graph TD
    A[User Query] --> B[Text-to-Query Service]
    B --> C[Query Cache Check]
    C --> D{Cache Hit?}
    D -->|Yes| E[Cached Results]
    D -->|No| F[Query Execution Engine]
    F --> G[Privacy Validation]
    G --> H[MongoDB Execution]
    H --> I[Result Sanitization]
    I --> J[Context Injection Service]
    E --> J
    J --> K[Enhanced Context]
    K --> L[Main LLM Response]
    L --> M[Cache Storage]
    M --> N[Final Response]
```

## Privacy and Security Features

### Access Control Matrix

| User Type | Allowed Collections |
|-----------|-------------------|
| Student | students, studentKnowledgeGraph, testHistory, aegisGrader, chatConversations, curriculumNodes, questions, aiQuestions |
| Teacher | students, studentKnowledgeGraph, testHistory, aegisGrader, curriculumNodes, questions, aiQuestions |

**Note:** Teachers are explicitly blocked from accessing `chatConversations` for privacy protection.

### Data Sanitization

1. **Sensitive Field Removal:**
   - `password`, `securityToken`, `email` (for teachers)
   - `metadata.ipAddress`, `metadata.device`, `metadata.browser`
   - System fields: `_id`, `__v`, `createdAt`, `updatedAt`

2. **ObjectId Anonymization:**
   - All ObjectIds are converted to anonymized hashes
   - Prevents exposure of internal database structure

3. **Query Structure Validation:**
   - Blocks dangerous operators: `$where`, `$expr`, `$function`
   - Prevents system collection access
   - Enforces result limits and timeouts

### DPDP Act 2023 Compliance

- **Data Minimization:** Only retrieves necessary data based on query context
- **Purpose Limitation:** Queries are restricted to educational purposes
- **Access Control:** Strict user-based data isolation
- **Audit Logging:** All data access attempts are logged
- **Consent Management:** Respects existing user consent preferences

## Performance Optimizations

### Caching Strategy

1. **Query Result Caching:**
   - 7-day TTL for query results
   - Cache keys based on query structure and user context
   - Intelligent cache warming for common queries

2. **Context Caching:**
   - Structured context is cached separately
   - Reduces LLM processing for repeated queries

3. **Cache Analytics:**
   - Hit rate monitoring and optimization
   - Automatic cleanup of low-hit entries

### Query Optimization

1. **Collection Targeting:**
   - Maximum 2 collections for normal analysis
   - Maximum 5 collections for deep analysis
   - Smart collection selection based on query intent

2. **Result Limiting:**
   - Default limit of 50 documents per query
   - Maximum 100 documents with safety override
   - Projection-based field limiting

3. **Execution Timeouts:**
   - 5-second maximum query execution time
   - Automatic fallback on timeout

## Integration Guide

### Frontend Integration

Update the AegisAI chat component to use the new endpoint:

```javascript
// ✅ UPDATED: Frontend now uses the new query system endpoint
const response = await axiosPrivate.post('/api/teaching-assistant/chat-query', {
  studentId,
  subject,
  message: messageContent,
  conversationHistory,
  conversationId: activeConversationId,
  userName: user?.firstName || user?.username || 'there'
});

// Old endpoint (now replaced):
// const response = await axiosPrivate.post('/api/teaching-assistant/chat-mcp', {
```

### Backend Integration

The system automatically falls back to the traditional method if any component fails:

```javascript
// Automatic fallback in enhancedTeachingAssistantService
if (!queryGeneration.success) {
  return this.fallbackToTraditionalMethod(req, res);
}
```

## Configuration

### Environment Variables

```env
# Required for LLM services
GEMINI_API_KEY=your_gemini_api_key

# Optional for privacy service
PRIVACY_SALT=your_privacy_salt_for_anonymization
```

### Service Configuration

Each service has configurable parameters:

```javascript
// textToQueryService.js
const queryGenerationModel = "gemini-2.0-flash";

// queryCacheService.js
const cacheConfig = {
  ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxEntries: 10000,
  cleanupInterval: 24 * 60 * 60 * 1000
};

// privacySecurityService.js
const dataMinimizationRules = {
  maxDocuments: 100,
  maxQueryTime: 5000,
  requireProjection: true
};
```

## Monitoring and Analytics

### Performance Metrics

- Query generation time
- Cache hit/miss rates
- Average response time
- Collections accessed per query
- Documents retrieved per query

### Privacy Audit

- All data access attempts are logged
- User type and collection access tracking
- Failed access attempt monitoring
- Compliance reporting capabilities

### Health Monitoring

Use the health check endpoint to monitor system status:

```bash
curl http://localhost:8080/api/teaching-assistant/health
```

## Testing

### Running Tests

```bash
# Run the comprehensive test suite
npm test backend/tests/textToMongoQuerySystem.test.js

# Run specific test categories
npm test -- --grep "Privacy and Security"
npm test -- --grep "Performance Tests"
```

### Test Coverage

- Query generation accuracy
- Privacy compliance validation
- Cache performance testing
- Integration testing
- Error handling and fallback testing

## Troubleshooting

### Common Issues

1. **Query Generation Failures:**
   - Check Gemini API key configuration
   - Verify network connectivity
   - Review query complexity

2. **Privacy Validation Errors:**
   - Check user permissions
   - Verify collection access rights
   - Review query structure for dangerous operators

3. **Cache Performance Issues:**
   - Monitor cache hit rates
   - Check TTL configuration
   - Review cache cleanup intervals

4. **Database Connection Issues:**
   - Verify MongoDB connection
   - Check model imports
   - Review collection naming patterns

### Debugging

Enable detailed logging:

```javascript
// Set environment variable for debug logging
DEBUG=query-system:* npm start
```

### Fallback Behavior

The system automatically falls back to the traditional mongoMcpService if:
- Query generation fails
- Query execution fails
- Privacy validation fails
- Any critical component is unavailable

## Future Enhancements

1. **Machine Learning Optimization:**
   - Query pattern learning
   - Automatic cache warming
   - Performance prediction

2. **Advanced Analytics:**
   - Query effectiveness scoring
   - User behavior analysis
   - Educational outcome correlation

3. **Extended Privacy Features:**
   - Differential privacy implementation
   - Advanced anonymization techniques
   - Consent management integration

4. **Performance Improvements:**
   - Query parallelization
   - Advanced caching strategies
   - Database index optimization

## Support

For technical support or questions about the Text-to-MongoDB Query System:

1. Check the health endpoint for system status
2. Review logs for error details
3. Consult the test suite for usage examples
4. Refer to the architecture documentation for system understanding
