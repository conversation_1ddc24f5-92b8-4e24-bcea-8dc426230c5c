import mongoose from 'mongoose';
import crypto from 'crypto';

const llmCacheSchema = new mongoose.Schema({
  studentId: {
    type: String,
    required: true,
  },
  subject: {
    type: String,
    required: true,
  },
  requestType: {
    type: String,
    required: true,
    enum: ['strategies', 'topic', 'next', 'gaps', 'chat', 'query_system'],
  },
  // For chat caching - composite key components
  conversationId: {
    type: String,
    required: function() { return this.requestType === 'chat'; }
  },
  messageHash: {
    type: String,
    required: function() { return this.requestType === 'chat'; }
  },
  // Cache key for efficient lookups
  cacheKey: {
    type: String,
    required: true,
    unique: true
  },
  response: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  // Metadata for analytics
  metadata: {
    promptTokens: { type: Number },
    responseTokens: { type: Number },
    modelUsed: { type: String },
    responseTime: { type: Number }, // in milliseconds
    hitCount: { type: Number, default: 1 }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastAccessed: {
    type: Date,
    default: Date.now
  }
});

// Compound indexes for efficient querying
llmCacheSchema.index({ studentId: 1, subject: 1, requestType: 1 });
// Note: cacheKey index is automatically created by unique: true option
llmCacheSchema.index({ conversationId: 1, requestType: 1 }); // Chat-specific queries
llmCacheSchema.index({ createdAt: 1 }, { expireAfterSeconds: 604800 }); // TTL index - 7 days
llmCacheSchema.index({ lastAccessed: 1 }); // Analytics index

// Static method to generate cache key
llmCacheSchema.statics.generateCacheKey = function(studentId, subject, requestType, conversationId = null, messageContent = null) {
  if (requestType === 'chat' && conversationId && messageContent) {
    // For chat: userId_conversationId_messageHash
    const messageHash = crypto.createHash('sha256').update(messageContent.trim().toLowerCase()).digest('hex').substring(0, 16);
    return `${studentId}_${conversationId}_${messageHash}`;
  } else {
    // For other requests: studentId_subject_requestType
    return `${studentId}_${subject}_${requestType}`;
  }
};

// Static method to generate message hash
llmCacheSchema.statics.generateMessageHash = function(messageContent) {
  return crypto.createHash('sha256').update(messageContent.trim().toLowerCase()).digest('hex').substring(0, 16);
};

// Instance method to increment hit count
llmCacheSchema.methods.incrementHitCount = function() {
  this.hitCount += 1;
  this.lastAccessed = new Date();
  return this.save();
};

const LLMCache = mongoose.model('LLMCache', llmCacheSchema);

export default LLMCache;