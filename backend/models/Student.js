import { Schema, model } from 'mongoose';
import pkg from 'bcryptjs';
import { TaskSchema } from './Tasks.js';

const { hash, compare } = pkg;

const ResponseSchema = new Schema({
    questionId: { type: Schema.Types.ObjectId, ref: 'Question', required: true },
    selectedAnswer: { type: Number || null }, // e.g., index 0-3
    isCorrect: { type: Boolean },
    intuition: { type: String }, // Optional: explanation of choice
    timeSpent: { type: Number, required: true }, // Seconds per question
});

const StudentAnalyticsSchema = new Schema({
    // Individual performance metrics
    performanceMetrics: {
        averageTimePerQuestion: { type: Number },
        averageScore: { type: Number },
        currentProficiency: { type: Number },
        improvementRate: { type: Number },        // Rate of improvement over time
        // consistencyScore: { type: Number },
        totalTestsTaken: { type: Number },
        completionRate: { type: Number },         // Percentage of assigned tests completed
        // masteryAchieved: [{ 
        //     subtopicId: { type: Schema.Types.ObjectId, ref: 'Subtopic' },
        //     achievedDate: { type: Date }
        // }]
    },
    
    // Individual progress tracking
    progressTracking: {
        dailyActivity: [{ 
            date: { type: Date },
            minutesSpent: { type: Number },
            questionsAnswered: { type: Number },
            consistencyScore: { type: Number },
            averageSessionLength: { type: Number }

        }],
        // weeklyProgress: [{ 
        //     weekOf: { type: Date },
        //     averageProficiency: { type: Number },
        //     proficiencyChange: { type: Number }
        // }],
        // learningVelocity: { type: Number } // Rate of knowledge acquisition 
    },
    
    // Personal strengths and weaknesses
    knowledgeAnalysis: {
        strengths: [{
            subtopicId: { type: String},
            proficiency: { type: Number },
            confidenceScore: { type: Number },
            lastAssessed: { type: Date }
        }],
        weaknesses: [{
            subtopicId: { type: String},
            proficiency: { type: Number },
            recommendedPractice: { type: String },
            lastAssessed: { type: Date }
        }],
        // prerequisites: {
        //     missingPrerequisites: [{ 
        //         subtopicId: { type: String},
        //         weightage: { type: Number }
        //     }],
        //     solidFoundations: [{ type: String}]
        // }
    },
    
    // Learning behavior patterns
    learningBehavior: {
        errorPatterns: [{
            patternType: { type: String },  //using bloom taxonomy to classify error patterns like knowledge, comprehension, application, analysis, synthesis, evaluation
            frequency: { type: Number },
            commonMistakes: [String]
        }],
        learningStyle: {
            speedVsAccuracy: { type: Number },
            reflectionTime: { type: Number }, 
            retryFrequency: { type: Number }
        }
    },
    
    // Personalized recommendations
    personalRecommendations: {
        // nextTopics: [{
        //     subtopicId: { type: String},
        //     reasonCode: { type: String },
        //     priority: { type: Number }
        // }],
        practiceSuggestions: [{
            questionType: { type: String },
            targetSkill: { type: String },
            difficulty: { type: Number }
        }],
        estimatedMasteryTimeline: [{
            topicId: { type: String},
            estimatedDays: { type: Number }
        }]
    },
    
    lastUpdated: { type: Date, default: Date.now }
});


const SubjectSchema = new Schema({
    subjectClassId: { type: Schema.Types.ObjectId, ref: 'Class', required: true },
    subjectName: { type: String, required: true },
    overallProficiency: { type: Number, required: true, default: 0 },
    knowledgeGraphId:
    {
        type: Schema.Types.ObjectId,
        ref: 'StudentCurriculum', // Reference to StudentCurriculumModel
        required: true,
    },
    testHistory: [{ type: Schema.Types.ObjectId, ref: 'TestHistory' }],  // Array of test history for this subject
    attemptedTests: [{
        testId: { type: Schema.Types.ObjectId, ref: 'TestHistory', required: true },
        startTime: { type: Date }, // When the student started the test
        endTime: { type: Date }, // When the student submitted
        totalTimeTaken: { type: Number }, // In seconds (derived from start/end)
        responses: [ResponseSchema],
        totalScore: { type: Number, required: true },
        proficiencyBefore: { type: Number }, // Overall proficiency before attempt
        proficiencyAfter: { type: Number }, // Updated proficiency after attempt
        metadata: {
            device: { type: String }, // e.g., "Mobile", "Desktop"
            browser: { type: String }, // e.g., "Chrome"
            ipAddress: { type: String } // For detecting suspicious activity
        },
        flaggedForReview: { type: Boolean, default: false } // Cheating suspicion
    }],
    analytics: StudentAnalyticsSchema // Array of analytics for this subject
});

const StudentSchema = new Schema({
    username: { type: String, required: true, unique: true, index: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    email: { type: String, unique: true, index: true, sparse: true },
    admissionNumber: { type: String, unique: true, index: true, sparse: true },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    registeredAt: {
        type: Date,
        default: Date.now
    },
    password: { type: String, required: true },
    profileImage: { type: String, default: "" }, // <-- Add this line
    subjects: [SubjectSchema],                    // Array of subjects with sub-skills and test history
    role: { type: String, default: 'Student' },   // Role of the user (student, teacher, admin)
    securityToken: { type: String },
    schoolCode: { type: Schema.Types.String, ref: 'School' },  // Reference to the School collection
    tasks: [TaskSchema],
    classes: [{ type: Schema.Types.ObjectId, ref: 'Class' }], // Reference to the Class collection   
}, { collection: 'students' });

// Hash password before saving a new or updated student record
StudentSchema.pre('save', async function (next) {
    if (!this.isModified('password')) return next();
    this.password = await hash(this.password, 10);
    next();
});

// StudentSchema.index({
//     "subjects.$.subjectName": 1,
//     "subjects.$.analytics.progressTracking.dailyActivity.date": 1
// });

// Method to compare passwords during login or authentication
StudentSchema.methods.comparePassword = function (candidatePassword) {
    return compare(candidatePassword, this.password);
};

export default model('Student', StudentSchema);
