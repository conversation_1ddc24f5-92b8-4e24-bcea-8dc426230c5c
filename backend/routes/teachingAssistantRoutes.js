import express from 'express';
import {
  getChatbotResponse,
  getChatbotResponseWithMCP,
  getChatbotResponseWithQuerySystem,
  healthCheck
} from '../controllers/teachingAssistantController.js';

const router = express.Router();

// Get chatbot response (traditional endpoint for conversational AI)
router.post('/chat', getChatbotResponse);

// Get enhanced chatbot response with MongoDB MCP integration
// Now handles both student and teacher requests automatically
router.post('/chat-mcp', getChatbotResponseWithMCP);

// NEW: Get chatbot response with text-to-MongoDB query system
// Optimized data retrieval with targeted queries
router.post('/chat-query', getChatbotResponseWithQuerySystem);

// Health check endpoint for the new query system
router.get('/health', healthCheck);

export default router;