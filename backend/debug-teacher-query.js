import mongoose from 'mongoose';
import { config } from 'dotenv';
import Teacher from './models/Teacher.js';
import Class from './models/Class.js';
import Student from './models/Student.js';
import textToQueryService from './services/textToQueryService.js';
import queryExecutionEngine from './services/queryExecutionEngine.js';

config();

/**
 * Debug script to trace teacher query processing
 */

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGO_URI_TEST || process.env.MONGO_URI_PROD;
    if (!mongoUri) {
      throw new Error('No MongoDB URI found in environment variables');
    }
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function debugTeacherQuery() {
  console.log('\n🔍 Debugging Teacher Query Processing...\n');

  try {
    // Find a teacher with classes
    const teacher = await Teacher.findOne().populate('classes');
    
    if (!teacher) {
      console.log('❌ No teachers found in database');
      return;
    }

    console.log(`👨‍🏫 Testing with teacher: ${teacher.username} (ID: ${teacher._id})`);
    
    // Get teacher's classes with student data
    const teacherClasses = await Class.find({ teacherId: teacher._id }).populate('students');
    console.log(`📚 Teacher has ${teacherClasses.length} classes`);
    
    for (const cls of teacherClasses) {
      console.log(`  - Class: ${cls.className} (${cls.subject}) - ${cls.students?.length || 0} students`);
    }

    // Test the query processing pipeline
    const testQuery = "How is my class performing in mathematics?";
    const teacherStudentId = `teacher_${teacher._id}_class_${teacherClasses[0]?._id || 'no-class'}`;
    
    console.log(`\n🧪 Testing query: "${testQuery}"`);
    console.log(`👤 Teacher studentId pattern: ${teacherStudentId}`);

    // Step 1: Test text-to-query service
    console.log('\n📝 Step 1: Text-to-Query Service');
    try {
      const queryGeneration = await textToQueryService.generateQuery(
        testQuery,
        'teacher',
        teacher._id.toString(),
        'Mathematics',
        'normal',
        []
      );

      console.log('Query generation result:', JSON.stringify(queryGeneration, null, 2));

      if (queryGeneration.success && queryGeneration.queries) {
        // Step 2: Test query execution
        console.log('\n⚡ Step 2: Query Execution');
        
        const queryExecution = await queryExecutionEngine.executeQueries(
          queryGeneration.queries,
          'teacher',
          teacher._id.toString(),
          'Mathematics'
        );

        console.log('Query execution result:', JSON.stringify(queryExecution, null, 2));

        if (queryExecution.success) {
          console.log(`✅ Query execution successful`);
          console.log(`📊 Total documents retrieved: ${queryExecution.summary?.totalDocuments || 0}`);
          console.log(`🗂️  Collections accessed: ${queryExecution.summary?.collectionsAccessed?.join(', ') || 'none'}`);
          
          // Show sample data if available
          if (queryExecution.results && queryExecution.results.length > 0) {
            queryExecution.results.forEach((result, index) => {
              console.log(`\n📋 Result ${index + 1} (${result.collection}):`);
              console.log(`  - Success: ${result.success}`);
              console.log(`  - Document count: ${result.documentCount || 0}`);
              if (result.data && Array.isArray(result.data) && result.data.length > 0) {
                console.log(`  - Sample data:`, JSON.stringify(result.data[0], null, 2));
              } else {
                console.log(`  - No data returned`);
              }
            });
          }
        } else {
          console.log(`❌ Query execution failed: ${queryExecution.error}`);
        }
      } else {
        console.log(`❌ Query generation failed or fell back to traditional method`);
        if (queryGeneration.reason) {
          console.log(`   Reason: ${queryGeneration.reason}`);
        }
      }
    } catch (error) {
      console.error('❌ Error in query processing:', error);
    }

    // Step 3: Check if teacher has actual data to analyze
    console.log('\n📊 Step 3: Teacher Data Availability Check');
    
    const totalStudents = teacherClasses.reduce((sum, cls) => sum + (cls.students?.length || 0), 0);
    console.log(`👥 Total students across all classes: ${totalStudents}`);
    
    if (totalStudents === 0) {
      console.log('⚠️  ISSUE IDENTIFIED: Teacher has no students in their classes');
      console.log('   This explains why the AI gives generic responses - there\'s no data to analyze');
      
      // Let's add some test students to one of the classes
      if (teacherClasses.length > 0) {
        console.log('\n🔧 Adding test students to verify the system works...');
        
        // Find some existing students to add to the class
        const existingStudents = await Student.find().limit(3);
        if (existingStudents.length > 0) {
          const testClass = teacherClasses[0];
          testClass.students = existingStudents.map(s => s._id);
          await testClass.save();
          
          console.log(`✅ Added ${existingStudents.length} test students to class: ${testClass.className}`);
          
          // Re-test the query with students
          console.log('\n🔄 Re-testing query with students added...');
          
          const retestQueryExecution = await queryExecutionEngine.executeQueries(
            [{
              collection: 'students',
              operation: 'find',
              query: {},
              projection: 'username subjects.subjectName subjects.overallProficiency'
            }],
            'teacher',
            teacher._id.toString(),
            'Mathematics'
          );
          
          console.log('Re-test results:', JSON.stringify(retestQueryExecution, null, 2));
        }
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

async function runDebug() {
  await connectToDatabase();
  await debugTeacherQuery();
  
  console.log('\n🎯 Debug Summary:');
  console.log('- Checked teacher data access implementation');
  console.log('- Tested text-to-query service for teacher context');
  console.log('- Verified query execution engine filtering');
  console.log('- Identified potential data availability issues');
  
  await mongoose.disconnect();
  console.log('\n✅ Debug completed and database disconnected');
}

// Run the debug
runDebug().catch(console.error);
